# Voucher Management System (VMS) 2.0

A comprehensive voucher management system for financial operations, designed for use in a local area network (LAN) environment.

## System Requirements

- Node.js 18.x or higher
- MySQL 8.0 or higher
- Modern web browser (Chrome, Firefox, Edge)
- Network connectivity for LAN access

## Features

- Multi-department voucher management
- Role-based access control
- Real-time updates and notifications
- Concurrent user support with resource locking
- Audit trail and logging
- Backup and restore functionality
- Comprehensive reporting and analytics

## Installation

### 1. <PERSON><PERSON> the Repository

```bash
git clone https://github.com/your-organization/vms-system-2025.git
cd vms-system-2025
```

### 2. Install Dependencies

```bash
# Install client dependencies
npm install

# Install server dependencies
cd server
npm install
cd ..
```

### 3. Configure the Database

1. Create a MySQL database
2. Update the database configuration in `server/.env`

```
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=vms_database
```

### 4. Initialize the Database

```bash
npm run setup:db
```

This will create all necessary tables and initial admin user.

## Running the System

### Start the Complete System (Server + Client)

```bash
npm run start:all
```

This will start both the backend server and frontend client.

### Start Only the Server

```bash
npm run start:server
```

### Start Only the Client

```bash
npm run start
```

## Default Users

The system comes with the following default users:

| Username | Password | Department | Role |
|----------|----------|------------|---------|
| ADMIN | admin123 | SYSTEM ADMIN | admin |
| FINANCE_USER | department123 | FINANCE | manager |
| AUDIT_USER | department123 | AUDIT | manager |
| MINISTRIES_USER | department123 | MINISTRIES | operator |
| PENSIONS_USER | department123 | PENSIONS | operator |
| PENTMEDIA_USER | department123 | PENTMEDIA | operator |
| MISSIONS_USER | department123 | MISSIONS | operator |
| PENTSOS_USER | department123 | PENTSOS | operator |

## Setting Up as a LAN Server

To make the system accessible to other computers on the local network:

1. Find the IP address of the server machine:
   - On Windows: Open Command Prompt and type `ipconfig`
   - On macOS/Linux: Open Terminal and type `ifconfig` or `ip addr`

2. Update the client configuration in `src/lib/api.ts` and `src/lib/socket.ts` to use the server's IP address:

```typescript
// In api.ts
const api = axios.create({
  baseURL: 'http://SERVER_IP_ADDRESS:8080/api',
  // ...
});

// In socket.ts
socket = io('http://SERVER_IP_ADDRESS:8081', {
  // ...
});
```

3. Build the client for production:

```bash
npm run build
```

4. Serve the built client:

```bash
npm run preview -- --host
```

5. Other computers on the network can access the system by navigating to:

```
http://SERVER_IP_ADDRESS:4173
```

## Technologies Used

- **Frontend**: React, TypeScript, Vite, shadcn/ui, Tailwind CSS, Zustand
- **Backend**: Node.js, Express, Socket.IO, MySQL
- **Authentication**: JWT-based authentication
- **Real-time**: WebSocket for real-time updates

## Concurrency Management

The system implements a granular concurrency approach that:

- Allows parallel work on different vouchers and tabs
- Prevents conflicting actions on the same resources
- Maintains data integrity during concurrent operations
- Provides real-time lock status updates to users
- Automatically expires locks after inactivity

## Backup and Restore

The system includes automatic backup functionality:

- Scheduled daily backups
- Manual backup option in the admin dashboard
- Restore from backup option
- Backup files stored in `server/backups` directory

## Troubleshooting

If you encounter any issues:

1. Check the server logs in `server/logs` directory
2. Verify database connection settings
3. Ensure MySQL server is running
4. Check network connectivity for LAN access
