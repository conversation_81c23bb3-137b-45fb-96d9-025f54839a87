# VMS 3.0 - Production Setup

This is a production-ready setup for the Voucher Management System (VMS) designed to run on a LAN environment.

## Prerequisites

- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Git](https://git-scm.com/downloads) (optional, for version control)

## Directory Structure

```
VMS 3.0/
├── client/           # Frontend files
├── server/           # Backend files
├── database/         # Database initialization scripts
│   └── init/         # SQL initialization scripts
├── docker/           # Docker configuration files
└── scripts/          # Utility scripts
```

## Setup Instructions

### 1. Prepare the Environment

1. Create the VMS 3.0 folder on your desktop
2. Copy the following files to their respective directories:

   - `docker-compose.yml` → VMS 3.0/
   - `Dockerfile.backend` → VMS 3.0/docker/
   - `Dockerfile.frontend` → VMS 3.0/docker/
   - `nginx.conf` → VMS 3.0/docker/
   - `init-db.sql` → VMS 3.0/database/init/
   - `deploy.ps1` → VMS 3.0/scripts/

### 2. Copy Application Files

1. Copy the backend files from your development environment:
   - Copy all files from `VM-system-2025/server/` to `VMS 3.0/server/`
   
2. Copy the frontend files from your development environment:
   - Copy all files from `VM-system-2025/` (excluding the server directory) to `VMS 3.0/client/`

### 3. Update Configuration Files

1. Update the backend configuration:
   - Edit `VMS 3.0/server/.env` to use the Docker environment variables:

   ```
   # Server Configuration
   PORT=8080
   NODE_ENV=production
   
   # WebSocket Configuration
   WS_PORT=8081
   
   # Database Configuration
   DB_HOST=mysql
   DB_PORT=3306
   DB_USER=vms_user
   DB_PASSWORD=vms_password
   DB_NAME=vms_production
   
   # JWT Secret
   JWT_SECRET=vms_secret_key_2025_1989
   JWT_EXPIRES_IN=24h
   
   # Logging
   LOG_LEVEL=info
   ```

2. Update the frontend configuration:
   - Edit `VMS 3.0/client/src/lib/api.ts` to use the correct backend URL:

   ```typescript
   // Create axios instance with base URL and default headers
   const api = axios.create({
     baseURL: '/api',  // This will be proxied by Nginx
     headers: {
       'Content-Type': 'application/json',
     },
   });
   ```

### 4. Deploy the Application

1. Open PowerShell as Administrator
2. Navigate to the VMS 3.0 directory:
   ```
   cd "$env:USERPROFILE\Desktop\VMS 3.0"
   ```
3. Run the deployment script:
   ```
   .\scripts\deploy.ps1
   ```

### 5. Access the Application

Once deployed, the application will be accessible at:
- http://localhost (from the server machine)
- http://<server-ip-address> (from other machines on the LAN)

### Default Login Credentials

- Department: SYSTEM ADMIN
- Username: System Administrator
- Password: admin123

## Maintenance

### Backing Up the Database

To back up the database:

```powershell
docker exec vms-mysql mysqldump -u root -p"vms@2025@1989" vms_production > backup_$(Get-Date -Format "yyyyMMdd").sql
```

### Restoring the Database

To restore the database from a backup:

```powershell
Get-Content backup_file.sql | docker exec -i vms-mysql mysql -u root -p"vms@2025@1989" vms_production
```

### Viewing Logs

To view logs for all services:

```powershell
docker-compose logs
```

To view logs for a specific service:

```powershell
docker-compose logs backend
docker-compose logs frontend
docker-compose logs mysql
```

### Stopping the Application

To stop all services:

```powershell
docker-compose down
```

### Restarting the Application

To restart all services:

```powershell
docker-compose restart
```

## Troubleshooting

### Database Connection Issues

If the backend cannot connect to the database:

1. Check if the MySQL container is running:
   ```
   docker ps | findstr mysql
   ```

2. Check the MySQL logs:
   ```
   docker-compose logs mysql
   ```

### Frontend Cannot Connect to Backend

If the frontend cannot connect to the backend:

1. Check if the backend container is running:
   ```
   docker ps | findstr backend
   ```

2. Check the backend logs:
   ```
   docker-compose logs backend
   ```

3. Verify the Nginx configuration:
   ```
   docker exec vms-frontend nginx -t
   ```

## Technical Requirements

- Docker Desktop
- At least 4GB of RAM
- At least 10GB of free disk space
- Network connectivity for LAN access
