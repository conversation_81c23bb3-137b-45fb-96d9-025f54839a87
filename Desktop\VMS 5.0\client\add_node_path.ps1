# Run this script as Administrator
$nodePath = "C:\Program Files\nodejs"
$npmPath = "$env:APPDATA\npm"

# Get the current PATH
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")

# Check if paths already exist
if (-not $currentPath.Contains($nodePath)) {
    $newPath = $currentPath + ";" + $nodePath
    [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
    Write-Host "Added Node.js to PATH"
}

if (-not $currentPath.Contains($npmPath)) {
    $newPath = $currentPath + ";" + $npmPath
    [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
    Write-Host "Added npm to PATH"
}

Write-Host "Please restart your terminal for changes to take effect" 