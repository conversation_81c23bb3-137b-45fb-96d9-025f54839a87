# VMS 3.0 Deployment Script

# Check if Docker is installed
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is installed
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "Docker Compose is not installed. Please install Docker Compose first." -ForegroundColor Red
    exit 1
}

# Start Docker services
Write-Host "Starting VMS 3.0 services..." -ForegroundColor Green
docker-compose up -d

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if services are running
$services = docker ps --format "{{.Names}}"
if ($services -match "vms-mysql" -and $services -match "vms-backend" -and $services -match "vms-frontend") {
    Write-Host "All services are running!" -ForegroundColor Green
    
    # Get the IP address of the host machine
    $ipAddress = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias Ethernet).IPAddress
    if (-not $ipAddress) {
        $ipAddress = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi").IPAddress
    }
    
    Write-Host "VMS 3.0 is now accessible at:" -ForegroundColor Cyan
    Write-Host "http://$ipAddress" -ForegroundColor Cyan
    Write-Host "or" -ForegroundColor Cyan
    Write-Host "http://localhost" -ForegroundColor Cyan
} else {
    Write-Host "Some services failed to start. Please check the logs:" -ForegroundColor Red
    Write-Host "docker-compose logs" -ForegroundColor Yellow
}

Write-Host "Deployment complete!" -ForegroundColor Green
