version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: vms-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: vms@2025@1989
      MYSQL_DATABASE: vms_production
      MYSQL_USER: vms_user
      MYSQL_PASSWORD: vms_password
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - vms-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Server
  backend:
    build:
      context: ./server
      dockerfile: ./docker/Dockerfile.backend
    container_name: vms-backend
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: vms_user
      DB_PASSWORD: vms_password
      DB_NAME: vms_production
      PORT: 8080
      WS_PORT: 8081
      JWT_SECRET: vms_secret_key_2025_1989
      JWT_EXPIRES_IN: 24h
    ports:
      - "8080:8080"
      - "8081:8081"
    volumes:
      - ./server:/app
      - /app/node_modules
    networks:
      - vms-network

  # Frontend
  frontend:
    build:
      context: ./client
      dockerfile: ./docker/Dockerfile.frontend
    container_name: vms-frontend
    restart: always
    depends_on:
      - backend
    ports:
      - "80:80"
    networks:
      - vms-network

networks:
  vms-network:
    driver: bridge

volumes:
  mysql-data:
