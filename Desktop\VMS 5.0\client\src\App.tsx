import { Toaster as SonnerToaster } from "sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "@/components/theme-provider";
import { useEffect } from "react";
import { useAppStore } from "@/lib/store";
import { initializeDefaultData } from "@/lib/store/slices/initialize-data";
import { initializeGlobalToast } from "@/lib/toast-global";

import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import AuditDashboard from "./pages/AuditDashboard";
import AuditVoucherView from "./pages/AuditVoucherView";
import AuditAnalytics from "./pages/AuditAnalytics";
import VoucherDetails from "./pages/VoucherDetails";
import ProvisionalCashRecord from "./pages/ProvisionalCashRecord";
import AdminDashboard from "./pages/AdminDashboard";
import MinistriesDashboard from "./pages/MinistriesDashboard";
import PensionsDashboard from "./pages/PensionsDashboard";
import PentmediaDashboard from "./pages/PentmediaDashboard";
import MissionsDashboard from "./pages/MissionsDashboard";
import PentsosDashboard from "./pages/PentsosDashboard";
import Reset from "./reset";
import Register from "./pages/Register";

const queryClient = new QueryClient();

// Initialize app component
const AppContent = () => {
  const { fetchCurrentUser, fetchVouchers, currentUser, logout } = useAppStore();

  // Initialize app on mount
  useEffect(() => {
    const initializeApp = async () => {
      // Initialize default users if needed
      initializeDefaultData();

      // Initialize global toast function
      initializeGlobalToast();
      console.log('Global toast function initialized');

      // Try to fetch current user from token
      const success = await fetchCurrentUser();

      // If user is authenticated, fetch initial data
      if (success && currentUser) {
        // Fetch vouchers based on user department
        if (currentUser.department === 'AUDIT' || currentUser.department === 'SYSTEM ADMIN') {
          // Audit and admin see all vouchers
          fetchVouchers();
        } else {
          // Other departments see only their vouchers
          fetchVouchers(currentUser.department);
        }
      }
    };

    initializeApp();
  }, []);

  // Add auto-logout on browser close
  useEffect(() => {
    const handleBeforeUnload = async () => {
      if (currentUser) {
        try {
          // Attempt to logout when browser is closed
          await logout();
          console.log('Auto-logout on page close successful');
        } catch (error) {
          console.error('Auto-logout error:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [currentUser, logout]);

  // Add inactivity detection for auto-logout
  useEffect(() => {
    if (!currentUser) return; // Only track activity when user is logged in

    let inactivityTimer: number | undefined;
    const INACTIVITY_TIMEOUT = 10 * 60 * 1000; // 10 minutes in milliseconds

    const resetInactivityTimer = () => {
      if (inactivityTimer) {
        window.clearTimeout(inactivityTimer);
      }

      inactivityTimer = window.setTimeout(async () => {
        console.log('User inactive for 10 minutes, logging out...');
        await logout();
        alert('You have been logged out due to inactivity after 10 minutes.');
        window.location.href = '/';
      }, INACTIVITY_TIMEOUT);
    };

    // Reset timer on user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    // Start the initial timer
    resetInactivityTimer();

    // Add event listeners for user activity
    activityEvents.forEach(event => {
      window.addEventListener(event, resetInactivityTimer);
    });

    // Cleanup
    return () => {
      if (inactivityTimer) {
        window.clearTimeout(inactivityTimer);
      }

      activityEvents.forEach(event => {
        window.removeEventListener(event, resetInactivityTimer);
      });
    };
  }, [currentUser, logout]);

  return (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="dark" storageKey="voucher-management-theme">
      <TooltipProvider>
        <SonnerToaster />
        <Toaster />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Login />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/dashboard/voucher/:voucherId" element={<VoucherDetails />} />
            <Route path="/ministries-dashboard" element={<MinistriesDashboard />} />
            <Route path="/pensions-dashboard" element={<PensionsDashboard />} />
            <Route path="/pentmedia-dashboard" element={<PentmediaDashboard />} />
            <Route path="/missions-dashboard" element={<MissionsDashboard />} />
            <Route path="/pentsos-dashboard" element={<PentsosDashboard />} />
            <Route path="/audit-dashboard" element={<AuditDashboard />} />
            <Route path="/audit-dashboard/voucher/:voucherId" element={<AuditVoucherView />} />
            <Route path="/audit-dashboard/cash-record" element={<ProvisionalCashRecord />} />
            <Route path="/audit-dashboard/analytics" element={<AuditAnalytics />} />
            <Route path="/admin-dashboard" element={<AdminDashboard />} />
            <Route path="/reset" element={<Reset />} />
            <Route path="/register" element={<Register />} />
            <Route path="/admin-access" element={<AdminDashboard />} />
            <Route path="*" element={<Login />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);
};

const App = () => <AppContent />;

export default App;
