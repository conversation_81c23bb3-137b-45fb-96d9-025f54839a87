import { useState, useEffect } from 'react';
import { useAppStore } from '@/lib/store/hooks';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, CheckCircle, XCircle, Clock } from 'lucide-react';
import { PendingRegistration } from '@/lib/store/types';
import { formatStandardDate } from '@/lib/store/utils';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export function PendingRegistrationsSection() {
  const store = useAppStore();
  const { pendingRegistrations, fetchPendingRegistrations } = store;

  // Refresh data when component mounts and periodically
  useEffect(() => {
    // Fetch immediately
    fetchPendingRegistrations();

    // Set up interval to refresh every 5 seconds
    const intervalId = setInterval(() => {
      fetchPendingRegistrations();
    }, 5000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [fetchPendingRegistrations]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegistration, setSelectedRegistration] = useState<PendingRegistration | null>(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);

  // Filtered registrations based on search term
  const filteredRegistrations = pendingRegistrations.filter(reg =>
    reg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reg.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleApproveRegistration = (registration: PendingRegistration) => {
    setSelectedRegistration(registration);
    setIsApproveDialogOpen(true);
  };

  const handleRejectRegistration = (registration: PendingRegistration) => {
    setSelectedRegistration(registration);
    setIsRejectDialogOpen(true);
  };

  const confirmApproval = async () => {
    if (!selectedRegistration) return;

    try {
      await store.approvePendingUser(selectedRegistration.id);
      toast.success(`Registration for ${selectedRegistration.name} approved successfully`);
      setIsApproveDialogOpen(false);
      setSelectedRegistration(null);

      // Refresh data after approval
      store.fetchPendingRegistrations();
      store.fetchAllUsers();
    } catch (error) {
      toast.error('Failed to approve registration', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    }
  };

  const confirmRejection = async () => {
    if (!selectedRegistration) return;

    try {
      await store.rejectPendingUser(selectedRegistration.id);
      toast.success(`Registration for ${selectedRegistration.name} rejected`);
      setIsRejectDialogOpen(false);
      setSelectedRegistration(null);

      // Refresh data after rejection
      store.fetchPendingRegistrations();
    } catch (error) {
      toast.error('Failed to reject registration', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    }
  };

  return (
    <div className="p-6">
      <div className="mb-4">
        <h2 className="text-2xl font-bold">Pending Registrations</h2>
        <p className="text-muted-foreground">Approve or reject user registration requests</p>
      </div>

      <div className="flex justify-between items-center mb-4">
        <div className="relative w-72">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search registrations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>

                <TableHead>Department</TableHead>
                <TableHead>Date Requested</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRegistrations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                    {searchTerm ? "No registrations matching your search" : "No pending registrations"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredRegistrations.map((registration) => (
                  <TableRow key={registration.id}>
                    <TableCell className="font-medium">{registration.name}</TableCell>

                    <TableCell>{registration.department}</TableCell>
                    <TableCell className="text-muted-foreground">
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        <span>
                          {registration.dateRequested
                            ? formatStandardDate(new Date(registration.dateRequested))
                            : 'Date not available'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center text-green-600"
                          onClick={() => handleApproveRegistration(registration)}
                        >
                          <CheckCircle className="mr-1 h-4 w-4" />
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center text-red-600"
                          onClick={() => handleRejectRegistration(registration)}
                        >
                          <XCircle className="mr-1 h-4 w-4" />
                          Reject
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Approve Registration Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Registration</DialogTitle>
            <DialogDescription>
              {selectedRegistration ?
                `Are you sure you want to approve the registration for ${selectedRegistration.name}?` :
                'Approve user registration'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedRegistration && (
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">Name:</span> {selectedRegistration.name}
                </div>

                <div>
                  <span className="font-semibold">Department:</span> {selectedRegistration.department}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>Cancel</Button>
            <Button onClick={confirmApproval} className="bg-green-600 hover:bg-green-700">Approve</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Registration Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Registration</DialogTitle>
            <DialogDescription>
              {selectedRegistration ?
                `Are you sure you want to reject the registration for ${selectedRegistration.name}?` :
                'Reject user registration'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedRegistration && (
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">Name:</span> {selectedRegistration.name}
                </div>

                <div>
                  <span className="font-semibold">Department:</span> {selectedRegistration.department}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>Cancel</Button>
            <Button onClick={confirmRejection} variant="destructive">Reject</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
