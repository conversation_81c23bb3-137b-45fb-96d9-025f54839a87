import { useState, useEffect } from 'react';
import { useAppStore } from '@/lib/store/hooks';
import { departments } from '@/lib/data';
import { usersApi } from '@/lib/api';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { PlusCircle, Search, Edit, Trash, Key, UserCog, Clock, Eye, EyeOff } from 'lucide-react';
import { User, UserRole } from '@/lib/store/types';
import { formatStandardDate } from '@/lib/store/utils';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from '@/components/ui/switch';

export function UserManagementSection() {
  const store = useAppStore();
  const { users, currentUser, fetchAllUsers } = store;

  // Refresh users data when component mounts
  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  const [searchTerm, setSearchTerm] = useState('');
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [userToResetPassword, setUserToResetPassword] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showNewUserPassword, setShowNewUserPassword] = useState(false);
  const [newUser, setNewUser] = useState<Partial<User>>({
    name: '',

    password: '',
    role: 'VIEWER',
    department: '',
    isActive: true,
  });

  // Filtered users based on search term
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'USER': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'VIEWER': return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const [userToChangeRole, setUserToChangeRole] = useState<{user: User, newRole: UserRole} | null>(null);
  const [isRoleChangeDialogOpen, setIsRoleChangeDialogOpen] = useState(false);
  const [isChangingRole, setIsChangingRole] = useState(false);

  const handleChangeUserRole = (userId: string, newRole: UserRole) => {
    const user = users.find(u => u.id === userId);
    if (!user) {
      toast.error('User not found');
      return;
    }

    setUserToChangeRole({user, newRole});
    setIsRoleChangeDialogOpen(true);
  };

  const confirmRoleChange = async () => {
    if (!userToChangeRole) return;

    setIsChangingRole(true);
    try {
      // Update the user with the new role directly via API
      const userData = {
        ...userToChangeRole.user,
        role: userToChangeRole.newRole
      };

      console.log(`Updating user ${userToChangeRole.user.id} role to ${userToChangeRole.newRole}`);

      // Call the API directly
      const result = await usersApi.updateUser(userToChangeRole.user.id, { role: userToChangeRole.newRole });
      console.log('Role update result from API:', result);

      // Update the local store
      await store.updateUser(userToChangeRole.user.id, { role: userToChangeRole.newRole });

      toast.success(`User role updated to ${userToChangeRole.newRole} successfully`);
      setIsRoleChangeDialogOpen(false);
      setUserToChangeRole(null);

      // Refresh the users list to ensure we have the latest data
      await fetchAllUsers();
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsChangingRole(false);
    }
  };

  const handleEditUser = (user: User) => {
    // Store the original isActive value for comparison later
    setEditingUser({
      ...user,
      _originalIsActive: user.isActive
    });
  };

  const handleRowClick = (user: User) => {
    // Create a dialog with all possible actions
    // Store the original isActive value for comparison later
    setEditingUser({
      ...user,
      _originalIsActive: user.isActive
    });
  };

  const [isSaving, setIsSaving] = useState(false);

  const handleSaveUserEdit = async () => {
    if (!editingUser) return;

    setIsSaving(true);
    try {
      // Validate required fields
      if (!editingUser.name || !editingUser.department) {
        toast.error('Name and Department are required fields');
        setIsSaving(false);
        return;
      }

      // Ensure name and department are uppercase for consistency
      // Make sure isActive is explicitly a boolean
      const updatedUser = {
        ...editingUser,
        name: editingUser.name.toUpperCase(),
        department: editingUser.department.toUpperCase(),
        isActive: Boolean(editingUser.isActive)
      };

      // Log the update for debugging
      console.log('Updating user with data:', updatedUser);
      console.log('isActive value:', updatedUser.isActive, 'type:', typeof updatedUser.isActive);

      // First update just the isActive field if it has changed
      if (updatedUser.isActive !== Boolean(editingUser._originalIsActive)) {
        console.log(`Updating isActive separately: ${updatedUser.isActive}`);
        try {
          // Update only the isActive field first
          const activeResult = await usersApi.updateUser(updatedUser.id, {
            isActive: updatedUser.isActive
          });
          console.log('isActive update result:', activeResult);
        } catch (activeError) {
          console.error('Error updating isActive:', activeError);
        }
      }

      // Then update the rest of the user data
      const result = await usersApi.updateUser(updatedUser.id, updatedUser);
      console.log('Update result from API:', result);

      // Update the local store
      await store.updateUser(updatedUser.id, updatedUser);

      toast.success('User updated successfully');
      setEditingUser(null);

      // Refresh the users list to ensure we have the latest data
      await fetchAllUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const [isAddingUser, setIsAddingUser] = useState(false);

  const handleAddUser = async () => {
    // Validate form
    if (!newUser.name || !newUser.password || !newUser.department) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsAddingUser(true);
    try {
      // Create a complete user object with required fields
      const userData = {
        name: newUser.name.toUpperCase(), // Ensure name is uppercase for consistency
        password: newUser.password,
        role: newUser.role as UserRole,
        department: newUser.department.toUpperCase(), // Ensure department is uppercase
        isActive: newUser.isActive || true
      };

      // Call the API to create the user in the database
      const createdUser = await usersApi.createUser(userData);

      // Add the user to the store
      store.addUser({
        ...createdUser,
        password: newUser.password // Store password in local state for development purposes
      });

      toast.success('User added successfully');
      setIsAddUserDialogOpen(false);
      setNewUser({
        name: '',
        password: '',
        role: 'VIEWER',
        department: '',
        isActive: true,
      });

      // Force a reload to ensure the user appears in the login dropdown
      toast.info('Refreshing page to update user list...');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      toast.error('Failed to add user', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      setIsAddingUser(false);
    }
  };

  const handleUpdateUser = (userId: string, updates: Partial<User>) => {
    store.updateUser(userId, updates);
  };

  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      // Call the API to delete the user
      await usersApi.deleteUser(userToDelete.id);

      // Update local state
      store.deleteUser(userToDelete.id);

      toast.success(`User ${userToDelete.name} deleted successfully`);
      setIsDeleteDialogOpen(false);
      setUserToDelete(null);

      // Refresh the users list to ensure it's up to date
      fetchAllUsers();
    } catch (error) {
      toast.error('Failed to delete user', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    }
  };

  const handleResetPassword = (user: User) => {
    setUserToResetPassword(user);
    setNewPassword('');
    setIsResetPasswordDialogOpen(true);
  };

  const handleSaveNewPassword = () => {
    if (!userToResetPassword || !newPassword) {
      toast.error('Please enter a new password');
      return;
    }

    try {
      // Use the dedicated resetUserPassword function
      const success = store.resetUserPassword(userToResetPassword.id, newPassword);

      if (!success) {
        throw new Error('Failed to reset password');
      }

      // If this is the current user, update their password in the session
      if (currentUser && currentUser.id === userToResetPassword.id) {
        store.setMockAdminUser({ ...currentUser, password: newPassword });
      }

      toast.success(`Password reset successfully for ${userToResetPassword.name}`);
      setIsResetPasswordDialogOpen(false);
      setUserToResetPassword(null);
      setNewPassword('');

      // Force a reload to ensure all state is properly updated
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      toast.error('Failed to reset password', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    }
  };

  return (
    <div className="p-6">
      <div className="mb-4">
        <h2 className="text-2xl font-bold">User Management</h2>
        <p className="text-muted-foreground">Manage user accounts and permissions</p>
      </div>

      <div className="flex justify-between items-center mb-4">
        <div className="relative w-72">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>

        <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>
                Create a new user account. All fields are required.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={newUser.name}
                  onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                  placeholder="John Doe"
                />
              </div>



              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showNewUserPassword ? "text" : "password"}
                    value={newUser.password}
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                    placeholder="Enter password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowNewUserPassword(!showNewUserPassword)}
                    tabIndex={-1}
                  >
                    {showNewUserPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                    <span className="sr-only">
                      {showNewUserPassword ? "Hide password" : "Show password"}
                    </span>
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={newUser.department}
                  onValueChange={(value) => setNewUser({...newUser, department: value})}
                >
                  <SelectTrigger id="department">
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={newUser.role as string}
                  onValueChange={(value: UserRole) => setNewUser({...newUser, role: value})}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                    <SelectItem value="USER">User</SelectItem>
                    <SelectItem value="VIEWER">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={newUser.isActive}
                  onCheckedChange={(checked) => setNewUser({...newUser, isActive: checked})}
                />
                <Label htmlFor="isActive">Account Active</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)} disabled={isAddingUser}>Cancel</Button>
              <Button onClick={handleAddUser} disabled={isAddingUser}>
                {isAddingUser ? 'Adding...' : 'Add User'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                    {searchTerm ? "No users matching your search" : "No users found"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow
                    key={user.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleRowClick(user)}
                  >
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>{user.department}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={`${getRoleBadgeColor(user.role)}`}>
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={user.isActive ? "success" : "secondary"} className={user.isActive ? "bg-green-500" : "bg-gray-500"}>
                        {user.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {user.lastLogin ? (
                        <div className="flex items-center">
                          <Clock className="mr-1 h-3 w-3" />
                          <span>{formatStandardDate(new Date(user.lastLogin))}</span>
                        </div>
                      ) : (
                        "Never"
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <UserCog className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEditUser(user)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleResetPassword(user)}>
                            <Key className="mr-2 h-4 w-4" />
                            Reset Password
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                          <DropdownMenuItem
                            disabled={user.role === 'ADMIN' || user.id === currentUser?.id}
                            onClick={() => handleChangeUserRole(user.id, 'ADMIN')}
                          >
                            Admin
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            disabled={user.role === 'USER' || user.id === currentUser?.id}
                            onClick={() => handleChangeUserRole(user.id, 'USER')}
                          >
                            User
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            disabled={user.role === 'VIEWER' || user.id === currentUser?.id}
                            onClick={() => handleChangeUserRole(user.id, 'VIEWER')}
                          >
                            Viewer
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600 focus:text-red-600"
                            disabled={user.id === currentUser?.id}
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      {editingUser && (
        <Dialog open={!!editingUser} onOpenChange={(open) => !open && setEditingUser(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Manage User: {editingUser.name}</DialogTitle>
              <DialogDescription>
                Update information or perform actions for this user
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Full Name</Label>
                <Input
                  id="edit-name"
                  value={editingUser.name}
                  onChange={(e) => setEditingUser({...editingUser, name: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-department">Department</Label>
                <Select
                  value={editingUser.department}
                  onValueChange={(value) => setEditingUser({...editingUser, department: value})}
                >
                  <SelectTrigger id="edit-department">
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-role">Role</Label>
                <Select
                  value={editingUser.role}
                  onValueChange={(value: UserRole) => setEditingUser({...editingUser, role: value})}
                >
                  <SelectTrigger id="edit-role">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN" disabled={editingUser.id === currentUser?.id}>Admin</SelectItem>
                    <SelectItem value="USER" disabled={editingUser.id === currentUser?.id}>User</SelectItem>
                    <SelectItem value="VIEWER" disabled={editingUser.id === currentUser?.id}>Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isActive"
                  checked={editingUser.isActive}
                  onCheckedChange={(checked) => setEditingUser({...editingUser, isActive: checked})}
                  disabled={editingUser.id === currentUser?.id}
                />
                <Label htmlFor="edit-isActive">Account Active</Label>
              </div>

              <div className="pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full mb-2 flex items-center justify-center"
                  onClick={() => {
                    handleResetPassword(editingUser);
                    setEditingUser(null);
                  }}
                >
                  <Key className="mr-2 h-4 w-4" />
                  Reset Password
                </Button>

                {editingUser.id !== currentUser?.id && (
                  <Button
                    variant="destructive"
                    className="w-full flex items-center justify-center"
                    onClick={() => {
                      handleDeleteUser(editingUser);
                      setEditingUser(null);
                    }}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete User
                  </Button>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingUser(null)} disabled={isSaving}>Cancel</Button>
              <Button onClick={handleSaveUserEdit} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Reset Password Dialog */}
      <Dialog open={isResetPasswordDialogOpen} onOpenChange={setIsResetPasswordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              {userToResetPassword ? `Reset password for ${userToResetPassword.name}` : 'Reset user password'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-password">New Password</Label>
              <div className="relative">
                <Input
                  id="new-password"
                  type={showPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Enter new password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResetPasswordDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveNewPassword}>Reset Password</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-destructive">Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {userToDelete && (
              <div className="space-y-2">
                <p><strong>Name:</strong> {userToDelete.name}</p>
                <p><strong>Department:</strong> {userToDelete.department}</p>
                <p><strong>Role:</strong> {userToDelete.role}</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={confirmDeleteUser}>Delete User</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Role Change Confirmation Dialog */}
      <Dialog open={isRoleChangeDialogOpen} onOpenChange={setIsRoleChangeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              Are you sure you want to change this user's role? This will affect their permissions in the system.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {userToChangeRole && (
              <div className="space-y-2">
                <p><strong>Name:</strong> {userToChangeRole.user.name}</p>
                <p><strong>Department:</strong> {userToChangeRole.user.department}</p>
                <p><strong>Current Role:</strong> {userToChangeRole.user.role}</p>
                <p><strong>New Role:</strong> <span className="font-bold">{userToChangeRole.newRole}</span></p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleChangeDialogOpen(false)} disabled={isChangingRole}>Cancel</Button>
            <Button onClick={confirmRoleChange} disabled={isChangingRole}>
              {isChangingRole ? 'Updating...' : 'Confirm Change'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}