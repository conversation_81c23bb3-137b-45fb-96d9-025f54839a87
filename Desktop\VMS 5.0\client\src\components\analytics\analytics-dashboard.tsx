import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SummaryCards } from './summary-cards';
import { PreAuditSavingsTable } from './pre-audit-savings-table';
import { UserActivityMetrics } from './user-activity-metrics';
import { ProvisionalCashAnalysis } from './provisional-cash-analysis';
import { useAnalyticsData } from '@/hooks/use-analytics-data';

export function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('month');
  const {
    summaryData,
    savingsData,
    userActivityData,
    provisionalCashData,
    isLoading
  } = useAnalyticsData(timeRange);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h2>
        <Tabs defaultValue="month" className="w-[300px]" onValueChange={(value) => setTimeRange(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="quarter">Quarter</TabsTrigger>
            <TabsTrigger value="year">Year</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-[600px]">
          <div className="text-xl">Loading analytics data...</div>
        </div>
      ) : (
        <>
          <SummaryCards data={summaryData} />

          <Card>
            <CardHeader>
              <CardTitle>Pre-audit Savings</CardTitle>
              <CardDescription>
                Savings made across departments (difference between voucher amount and certified amount)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PreAuditSavingsTable data={savingsData} />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Activity Metrics</CardTitle>
                <CardDescription>
                  Performance metrics for audit staff members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UserActivityMetrics data={userActivityData} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Provisional Cash Analysis</CardTitle>
                <CardDescription>
                  Analysis of provisional cash transactions and clearance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProvisionalCashAnalysis data={provisionalCashData} />
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
