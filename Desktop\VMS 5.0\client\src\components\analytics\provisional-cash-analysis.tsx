import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatNumberWithCommas } from '@/utils/formatUtils';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend
} from 'recharts';
import { Download } from 'lucide-react';

interface CashRecord {
  department: string;
  outstanding: number;
  cleared: number;
  total: number;
  clearanceRate: number;
  totalCertifiedAmount: number;
  transactions: Transaction[];
}

interface Transaction {
  id: string;
  voucherId: string;
  claimant: string;
  amount: number;
  currency: string;
  date: string;
  status: 'not-cleared' | 'cleared';
}

interface AgingData {
  range: string;
  value: number;
  count: number;
}

interface ProvisionalCashData {
  departmentRecords: CashRecord[];
  agingData: AgingData[];
  totalOutstanding: number;
  totalCleared: number;
  overallClearanceRate: number;
}

interface ProvisionalCashAnalysisProps {
  data: ProvisionalCashData;
}

export function ProvisionalCashAnalysis({ data }: ProvisionalCashAnalysisProps) {
  const [view, setView] = useState<'departments' | 'aging'>('departments');
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);
  const [sortField, setSortField] = useState<'voucherId' | 'claimant' | 'date' | 'amount' | 'status'>('amount');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [deptSortField, setDeptSortField] = useState<'department' | 'outstanding' | 'cleared' | 'total' | 'clearanceRate' | 'totalCertifiedAmount'>('outstanding');
  const [deptSortDirection, setDeptSortDirection] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<'all' | 'cleared' | 'not-cleared'>('all');

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

  // Get the selected department record
  const selectedDepartmentRecord = selectedDepartment
    ? data.departmentRecords.find(record => record.department === selectedDepartment)
    : null;

  // Handle transaction sorting
  const handleSort = (field: 'voucherId' | 'claimant' | 'date' | 'amount' | 'status') => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default direction
      setSortField(field);
      setSortDirection('desc'); // Default to descending for most fields
    }
  };

  // Handle department sorting
  const handleDeptSort = (field: 'department' | 'outstanding' | 'cleared' | 'total' | 'clearanceRate' | 'totalCertifiedAmount') => {
    if (deptSortField === field) {
      // Toggle direction if same field
      setDeptSortDirection(deptSortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default direction
      setDeptSortField(field);
      setDeptSortDirection('desc'); // Default to descending for most fields
    }
  };

  // Get sorted and filtered transactions
  const getSortedTransactions = () => {
    if (!selectedDepartmentRecord) return [];

    // First filter by status
    const filteredTransactions = [...selectedDepartmentRecord.transactions].filter(transaction => {
      if (statusFilter === 'all') return true;
      return transaction.status === statusFilter;
    });

    // Then sort the filtered transactions
    return filteredTransactions.sort((a, b) => {
      if (sortField === 'amount') {
        return sortDirection === 'asc' ? a.amount - b.amount : b.amount - a.amount;
      } else if (sortField === 'date') {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      } else if (sortField === 'status') {
        // Sort by status (not-cleared first for desc)
        const statusA = a.status === 'not-cleared' ? 1 : 0;
        const statusB = b.status === 'not-cleared' ? 1 : 0;
        return sortDirection === 'asc' ? statusA - statusB : statusB - statusA;
      } else {
        // String comparison for voucherId and claimant
        const valueA = a[sortField].toLowerCase();
        const valueB = b[sortField].toLowerCase();
        return sortDirection === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }
    });
  };

  // Get sorted department records
  const getSortedDepartments = () => {
    return [...data.departmentRecords].sort((a, b) => {
      if (deptSortField === 'department') {
        return deptSortDirection === 'asc'
          ? a.department.localeCompare(b.department)
          : b.department.localeCompare(a.department);
      } else if (deptSortField === 'outstanding') {
        return deptSortDirection === 'asc' ? a.outstanding - b.outstanding : b.outstanding - a.outstanding;
      } else if (deptSortField === 'cleared') {
        return deptSortDirection === 'asc' ? a.cleared - b.cleared : b.cleared - a.cleared;
      } else if (deptSortField === 'total') {
        return deptSortDirection === 'asc' ? a.total - b.total : b.total - a.total;
      } else if (deptSortField === 'clearanceRate') {
        return deptSortDirection === 'asc' ? a.clearanceRate - b.clearanceRate : b.clearanceRate - a.clearanceRate;
      } else { // totalCertifiedAmount
        return deptSortDirection === 'asc' ? a.totalCertifiedAmount - b.totalCertifiedAmount : b.totalCertifiedAmount - a.totalCertifiedAmount;
      }
    });
  };

  const renderAgingPieChart = () => {
    return (
      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data.agingData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              nameKey="range"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {data.agingData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [`GHS ${formatNumberWithCommas(value)}`, 'Amount']}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const renderDepartmentTable = () => {
    // Get sorted departments
    const sortedDepartments = getSortedDepartments();

    // Get sort icon for departments
    const getDeptSortIcon = (field: 'department' | 'outstanding' | 'cleared' | 'total' | 'clearanceRate' | 'totalCertifiedAmount') => {
      if (deptSortField !== field) return null;
      return deptSortDirection === 'asc' ? '↑' : '↓';
    };

    return (
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleDeptSort('department')}
              >
                Department {getDeptSortIcon('department')}
              </TableHead>
              <TableHead
                className="text-right cursor-pointer hover:bg-muted/50"
                onClick={() => handleDeptSort('outstanding')}
              >
                Outstanding (GHS) {getDeptSortIcon('outstanding')}
              </TableHead>
              <TableHead
                className="text-right cursor-pointer hover:bg-muted/50"
                onClick={() => handleDeptSort('cleared')}
              >
                Cleared (GHS) {getDeptSortIcon('cleared')}
              </TableHead>
              <TableHead
                className="text-right cursor-pointer hover:bg-muted/50"
                onClick={() => handleDeptSort('total')}
              >
                Total (GHS) {getDeptSortIcon('total')}
              </TableHead>
              <TableHead
                className="text-right cursor-pointer hover:bg-muted/50"
                onClick={() => handleDeptSort('totalCertifiedAmount')}
              >
                Certified Amount (GHS) {getDeptSortIcon('totalCertifiedAmount')}
              </TableHead>
              <TableHead
                className="text-right cursor-pointer hover:bg-muted/50"
                onClick={() => handleDeptSort('clearanceRate')}
              >
                Clearance Rate {getDeptSortIcon('clearanceRate')}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedDepartments.map((record) => (
              <TableRow
                key={record.department}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => setSelectedDepartment(record.department)}
              >
                <TableCell className="font-medium">{record.department}</TableCell>
                <TableCell className="text-right text-amber-600">{formatNumberWithCommas(record.outstanding)}</TableCell>
                <TableCell className="text-right text-green-600">{formatNumberWithCommas(record.cleared)}</TableCell>
                <TableCell className="text-right">{formatNumberWithCommas(record.total)}</TableCell>
                <TableCell className="text-right text-blue-600">{formatNumberWithCommas(record.totalCertifiedAmount)}</TableCell>
                <TableCell className="text-right">
                  <Badge variant={record.clearanceRate >= 75 ? "success" : (record.clearanceRate >= 50 ? "warning" : "destructive")}>
                    {record.clearanceRate}%
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  const renderAgingTable = () => {
    return (
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Age Range</TableHead>
              <TableHead className="text-right">Amount (GHS)</TableHead>
              <TableHead className="text-right">Count</TableHead>
              <TableHead className="text-right">Percentage</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.agingData.map((item) => {
              const totalValue = data.agingData.reduce((sum, item) => sum + item.value, 0);
              const percentage = (item.value / totalValue) * 100;

              return (
                <TableRow key={item.range}>
                  <TableCell className="font-medium">{item.range}</TableCell>
                  <TableCell className="text-right">{formatNumberWithCommas(item.value)}</TableCell>
                  <TableCell className="text-right">{item.count}</TableCell>
                  <TableCell className="text-right">
                    <Badge variant={
                      item.range.includes("0-30") ? "success" :
                      (item.range.includes("31-60") ? "warning" : "destructive")
                    }>
                      {percentage.toFixed(1)}%
                    </Badge>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    );
  };

  // Handle exporting transactions to Excel
  const handleExportTransactions = () => {
    if (!selectedDepartmentRecord) return;

    // Get the filtered transactions based on current status filter
    const transactionsToExport = getSortedTransactions();

    // Create a meaningful filename
    const statusText = statusFilter === 'all' ? 'all' : (statusFilter === 'cleared' ? 'cleared' : 'not-cleared');
    const filename = `${selectedDepartment.toLowerCase()}-${statusText}-transactions-${new Date().toISOString().split('T')[0]}`;

    // Create CSV content
    const headers = ['Voucher ID', 'Claimant', 'Date', 'Amount', 'Currency', 'Status'];
    const rows = transactionsToExport.map(transaction => [
      transaction.voucherId,
      transaction.claimant,
      transaction.date,
      transaction.amount.toString(),
      transaction.currency,
      transaction.status === 'cleared' ? 'Cleared' : 'Not-Cleared'
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create a Blob with the CSV content
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // Create a download link
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';

    // Add the link to the DOM
    document.body.appendChild(link);

    // Click the link to trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
  };

  // Render transactions for a selected department
  const renderTransactions = () => {
    if (!selectedDepartmentRecord) return null;

    // Get sorted transactions
    const sortedTransactions = getSortedTransactions();

    // Get sort icon for transactions
    const getSortIcon = (field: 'voucherId' | 'claimant' | 'date' | 'amount' | 'status') => {
      if (sortField !== field) return null;
      return sortDirection === 'asc' ? '↑' : '↓';
    };

    // Get sort icon for departments
    const getDeptSortIcon = (field: 'department' | 'outstanding' | 'cleared' | 'total' | 'clearanceRate' | 'totalCertifiedAmount') => {
      if (deptSortField !== field) return null;
      return deptSortDirection === 'asc' ? '↑' : '↓';
    };

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedDepartment(null)}
            >
              Back to Departments
            </Button>
            <h3 className="text-lg font-medium">{selectedDepartment} Transactions</h3>
          </div>

          <div className="flex items-center gap-2">
            <Tabs value={statusFilter} onValueChange={(v) => setStatusFilter(v as any)} className="w-[300px]">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="cleared">Cleared</TabsTrigger>
                <TabsTrigger value="not-cleared">Not-Cleared</TabsTrigger>
              </TabsList>
            </Tabs>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => handleExportTransactions()}
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('voucherId')}
                >
                  Voucher ID {getSortIcon('voucherId')}
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('claimant')}
                >
                  Claimant {getSortIcon('claimant')}
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('date')}
                >
                  Date {getSortIcon('date')}
                </TableHead>
                <TableHead
                  className="text-right cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('amount')}
                >
                  Amount (GHS) {getSortIcon('amount')}
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('status')}
                >
                  Status {getSortIcon('status')}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedTransactions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">No transactions found</TableCell>
                </TableRow>
              ) : (
                sortedTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-medium">{transaction.voucherId}</TableCell>
                    <TableCell>{transaction.claimant}</TableCell>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell className="text-right">{formatNumberWithCommas(transaction.amount)} {transaction.currency}</TableCell>
                    <TableCell>
                      <Badge variant={transaction.status === 'cleared' ? "success" : "warning"}>
                        {transaction.status === 'cleared' ? 'Cleared' : 'Not-Cleared'}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 border rounded-md">
            <div className="text-sm text-muted-foreground">Total Certified Amount</div>
            <div className="text-2xl font-bold text-blue-600">
              GHS {formatNumberWithCommas(selectedDepartmentRecord.totalCertifiedAmount)}
            </div>
          </div>
          <div className="p-4 border rounded-md">
            <div className="text-sm text-muted-foreground">Clearance Rate</div>
            <div className="text-2xl font-bold">
              <span className={selectedDepartmentRecord.clearanceRate >= 75 ? "text-green-600" : (selectedDepartmentRecord.clearanceRate >= 50 ? "text-amber-600" : "text-red-600")}>
                {selectedDepartmentRecord.clearanceRate}%
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {selectedDepartment ? (
        renderTransactions()
      ) : (
        <>
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <div className="text-2xl font-bold">
                GHS {formatNumberWithCommas(data.totalOutstanding)}
              </div>
              <p className="text-sm text-muted-foreground">
                Total outstanding provisional cash
              </p>
            </div>

            <Tabs value={view} onValueChange={(v) => setView(v as any)} className="w-[300px]">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="departments">Departments</TabsTrigger>
                <TabsTrigger value="aging">Aging Analysis</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {view === 'departments' ? (
            <>
              {renderDepartmentTable()}
              <div className="text-sm text-muted-foreground text-center pt-2">
                Overall clearance rate: <strong>{data.overallClearanceRate}%</strong>
              </div>
            </>
          ) : (
            <>
              {renderAgingPieChart()}
              {renderAgingTable()}
            </>
          )}
        </>
      )}
    </div>
  );
}
