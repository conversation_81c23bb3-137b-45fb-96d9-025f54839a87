
import { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/store';
import { Department } from '@/lib/types';
import { NewlyArrivedVouchers } from './newly-arrived-vouchers';
import { DepartmentVoucherHubs } from './department-voucher-hubs';
import { DepartmentVoucherHub } from '@/components/department-voucher-hub';
import { DepartmentProvisionalCash } from '@/components/department-provisional-cash';
import { VoucherBatchReceiving } from '@/components/voucher-batch-receiving';
import { toast } from '@/hooks/use-toast';
import { DispatchControls } from './dispatch-controls';
import { AuditVoucherBatchNotification } from './audit-voucher-batch-notification';

export function AuditDashboardContent() {
  const navigate = useNavigate();
  const location = useLocation();
  const currentUser = useAppStore((state) => state.currentUser);
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const vouchers = useAppStore((state) => state.vouchers);
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const sendVouchersFromAuditToDepartment = useAppStore((state) => state.sendVouchersFromAuditToDepartment);
  const notifications = useAppStore((state) =>
    currentUser ? state.getNotificationsForUser(currentUser.id) : []
  );

  const [activeBatchId, setActiveBatchId] = useState<string | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [showProvisionalCash, setShowProvisionalCash] = useState(false);
  const [selectedDispatchVouchers, setSelectedDispatchVouchers] = useState<string[]>([]);
  const [dispatchPerson, setDispatchPerson] = useState<string>("SELECT_PERSON");
  const [isNotificationBlinking, setIsNotificationBlinking] = useState(false);
  const [previousPath, setPreviousPath] = useState<string | null>(null);
  const [previousDepartment, setPreviousDepartment] = useState<Department | null>(null);

  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== 'AUDIT') {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  // Track location changes to implement redirection
  useEffect(() => {
    // Store the current path for potential redirection after receiving vouchers
    if (!showBatchReceiving && !activeBatchId) {
      setPreviousPath(location.pathname);
      setPreviousDepartment(selectedDepartment);
    }
  }, [location.pathname, showBatchReceiving, activeBatchId, selectedDepartment]);

  // Force redirection to receive vouchers if there are pending batches
  useEffect(() => {
    if (showBatchReceiving && !activeBatchId && selectedDepartment) {
      // Save the current department before redirecting
      setPreviousDepartment(selectedDepartment);
      // Redirect to main dashboard to show the notification
      setSelectedDepartment(null);
      setShowProvisionalCash(false);
      // Show notification to user
      toast({
        title: "Action Required",
        description: "You must receive new vouchers before proceeding.",
        variant: "destructive",
      });
    }
  }, [showBatchReceiving, activeBatchId, selectedDepartment]);

  const unreadNotifications = notifications.filter(n => !n.isRead);
  const unreadBatchNotifications = unreadNotifications.filter(
    n => n.type === 'NEW_BATCH' && !n.fromAudit
  );

  // Safely filter pending batches with error handling
  const pendingBatches = voucherBatches ? voucherBatches.filter(
    batch => batch && !batch.received && !batch.fromAudit
  ) : [];

  const handleOpenBatch = (batchId: string) => {
    // Store current state before opening batch
    if (selectedDepartment) {
      setPreviousDepartment(selectedDepartment);
    }
    setActiveBatchId(batchId);
  };

  const handleCloseBatch = () => {
    setActiveBatchId(null);

    // Restore previous state if it exists
    if (previousDepartment) {
      setSelectedDepartment(previousDepartment);
      // Clear the previous department to avoid unexpected redirects
      setPreviousDepartment(null);
    }
  };

  const handleDisabledClick = () => {
    setIsNotificationBlinking(true);
    setTimeout(() => {
      setIsNotificationBlinking(false);
    }, 3000);

    // Show toast message
    toast({
      title: "Action Required",
      description: "You must receive new vouchers before proceeding.",
      variant: "destructive",
    });
  };

  const handleSelectDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setShowProvisionalCash(false);
    // Reset selected vouchers when changing departments
    setSelectedDispatchVouchers([]);
    setDispatchPerson("SELECT_PERSON");
  };

  const handleShowProvisionalCash = (department: Department) => {
    setSelectedDepartment(department);
    setShowProvisionalCash(true);
  };

  const handleBack = () => {
    if (selectedDepartment) {
      setSelectedDepartment(null);
      setShowProvisionalCash(false);
      // Reset selected vouchers when going back
      setSelectedDispatchVouchers([]);
      setDispatchPerson("SELECT_PERSON");
    }
  };

  const handleDispatchSelection = (selectedIds: string[]) => {
    setSelectedDispatchVouchers(selectedIds);
  };

  const handleDispatchVouchers = () => {
    if (!selectedDepartment || selectedDispatchVouchers.length === 0) {
      toast({
        title: "Error",
        description: "Please select vouchers and a department.",
        variant: "destructive",
      });
      return;
    }

    if (!dispatchPerson || dispatchPerson === "SELECT_PERSON") {
      toast({
        title: "Error",
        description: "Please select a dispatch person.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Update vouchers with dispatcher information
      selectedDispatchVouchers.forEach(voucherId => {
        // Get the current voucher to check its status
        const voucher = vouchers.find(v => v.id === voucherId);
        if (!voucher) return;

        // Determine the status based on whether it's a return voucher
        const newStatus = voucher.pendingReturn ? "VOUCHER RETURNED" : "VOUCHER CERTIFIED";

        console.log(`Dispatching voucher ${voucherId} with status ${newStatus} by ${dispatchPerson}`);

        updateVoucher(voucherId, {
          auditDispatchedBy: dispatchPerson,
          dispatched: true,
          status: newStatus,
          // If it was pending return, mark it as returned
          isReturned: voucher.pendingReturn ? true : undefined,
          pendingReturn: voucher.pendingReturn ? false : undefined
        });
      });

      // Send vouchers to department
      sendVouchersFromAuditToDepartment(selectedDepartment, selectedDispatchVouchers);

      toast({
        title: "Success",
        description: `${selectedDispatchVouchers.length} vouchers dispatched to ${selectedDepartment}.`,
      });

      // Reset selection after dispatch
      setSelectedDispatchVouchers([]);
      setDispatchPerson("SELECT_PERSON");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to dispatch vouchers.",
        variant: "destructive",
      });
    }
  };

  const showBatchReceiving = pendingBatches && pendingBatches.length > 0;
  // Determine if the current view should be locked due to pending batches
  const isViewLocked = showBatchReceiving && !activeBatchId && pendingBatches.length > 0;

  return (
    <div className="container space-y-6">
      {/* Notification banner for new vouchers - similar to Finance side */}
      {showBatchReceiving && pendingBatches.length > 0 && (
        <AuditVoucherBatchNotification
          pendingBatchesCount={pendingBatches.length}
          onReceiveVouchers={() => pendingBatches[0]?.id ? handleOpenBatch(pendingBatches[0].id) : null}
          isBlinking={isNotificationBlinking}
        />
      )}

      {unreadBatchNotifications.length > 0 && (
        <div className="bg-amber-100 dark:bg-amber-900/20 p-4 rounded-lg flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
          <p className="text-sm text-amber-800 dark:text-amber-300 uppercase">
            YOU HAVE {unreadBatchNotifications.length} UNREAD NOTIFICATION{unreadBatchNotifications.length !== 1 ? 'S' : ''} FOR NEW VOUCHER BATCH{unreadBatchNotifications.length !== 1 ? 'ES' : ''}.
          </p>
        </div>
      )}

      {/* If there are pending batches and no active batch, force the user to see the newly arrived vouchers */}
      {isViewLocked ? (
        <div className="space-y-6 mb-8">
          <NewlyArrivedVouchers
            pendingBatches={pendingBatches}
            onOpenBatch={handleOpenBatch}
          />

          {/* Show department hubs but make them disabled/locked */}
          <div className="opacity-60 pointer-events-none">
            <DepartmentVoucherHubs
              onSelectDepartment={handleSelectDepartment}
              onShowProvisionalCash={handleShowProvisionalCash}
            />
          </div>
        </div>
      ) : selectedDepartment ? (
        showProvisionalCash ? (
          <DepartmentProvisionalCash
            department={selectedDepartment}
            onBack={handleBack}
          />
        ) : (
          <>
            <DepartmentVoucherHub
              department={selectedDepartment}
              auditUsers={getAuditUsers().filter(user => user !== "SELECT_PERSON")}
              onBackToHubs={handleBack}
            />

            {selectedDispatchVouchers.length > 0 && (
              <DispatchControls
                selectedDispatchVouchers={selectedDispatchVouchers}
                selectedDepartment={selectedDepartment}
                dispatchPerson={dispatchPerson}
                setDispatchPerson={setDispatchPerson}
                handleDispatchVouchers={handleDispatchVouchers}
              />
            )}
          </>
        )
      ) : (
        <>
          {showBatchReceiving && (
            <div className="space-y-6 mb-8">
              <NewlyArrivedVouchers
                pendingBatches={pendingBatches}
                onOpenBatch={handleOpenBatch}
              />
            </div>
          )}

          <DepartmentVoucherHubs
            onSelectDepartment={handleSelectDepartment}
            onShowProvisionalCash={handleShowProvisionalCash}
          />
        </>
      )}

      {activeBatchId && (
        <VoucherBatchReceiving
          batchId={activeBatchId}
          open={!!activeBatchId}
          onClose={handleCloseBatch}
        />
      )}
    </div>
  );

  function getAuditUsers() {
    return [
      "SELECT_PERSON",
      "FIRANG BOAKYE",
      "HARRISON A. SARPONG",
      "WILLIAM AKUAMOAH",
      "RICHARD ARTHUR",
      "SAMUEL ASIEDU",
      "SAMUEL CATOE",
      "BRIGHT OMANE",
      "KWAME A. FRIMPNG",
      "ABENA Y. FORSON",
      "ELIZABETH A. KWAKYE",
      "NAOMI O. ANSAH",
      "AKUA BRIFAA",
      "SYLVESTER M. NYARKO",
      "DEBORAH ANIM",
      "PRINCE O. BONSU",
      "GUEST"
    ];
  }
}
