import { ArrowDownCircle, Lock, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useEffect, useRef, useState } from 'react';

interface AuditVoucherBatchNotificationProps {
  pendingBatchesCount: number;
  onReceiveVouchers: () => void;
  isBlinking?: boolean;
}

export function AuditVoucherBatchNotification({ 
  pendingBatchesCount, 
  onReceiveVouchers,
  isBlinking = false
}: AuditVoucherBatchNotificationProps) {
  const notificationRef = useRef<HTMLDivElement>(null);
  const [blinking, setBlinking] = useState(isBlinking);
  
  // Use effect to handle blinking state and scrolling
  useEffect(() => {
    setBlinking(isBlinking);
    
    if (isBlinking && notificationRef.current) {
      notificationRef.current.scrollIntoView({ behavior: 'smooth' });
      
      // Stop blinking after 3 seconds
      const timer = setTimeout(() => {
        setBlinking(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [isBlinking]);
  
  // Early return if there are no batches to display
  if (pendingBatchesCount === 0) return null;
  
  // Notification message
  const notificationMessage = `YOU HAVE NEW VOUCHER${pendingBatchesCount !== 1 ? 'S' : ''} FROM FINANCE: RECEIVE TO PROCEED.`;

  return (
    <div 
      ref={notificationRef}
      className={`bg-amber-900/20 p-4 rounded-lg mb-6 border ${blinking ? 'animate-pulse border-amber-400' : 'border-amber-700/50'}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ArrowDownCircle className={`h-5 w-5 ${blinking ? 'text-amber-400' : 'text-amber-500'}`} />
          <div className="flex items-center">
            <p className={`text-sm ${blinking ? 'text-amber-200 font-semibold' : 'text-amber-300'}`}>
              {notificationMessage}
            </p>
            <Lock className={`ml-2 h-4 w-4 ${blinking ? 'text-amber-400' : 'text-amber-500'}`} />
          </div>
        </div>
        <Button size="sm" onClick={onReceiveVouchers} className="uppercase bg-amber-700 hover:bg-amber-800">
          Receive Vouchers
        </Button>
      </div>
    </div>
  );
}
