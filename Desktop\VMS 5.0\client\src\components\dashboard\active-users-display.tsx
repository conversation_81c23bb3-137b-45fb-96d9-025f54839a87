import { useEffect, useState, useCallback } from 'react';
import { getSocket } from '@/lib/socket';
import { useAppStore } from '@/lib/store';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, Lock, UserCheck, RefreshCw } from 'lucide-react';
import { User } from '@/lib/store/types';
import { toast } from '@/components/ui/use-toast';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ActiveUsersDisplayProps {
  department: string;
}

// Component for displaying editor status
interface EditorStatusDisplayProps {
  currentUser: User;
  activeEditor: User | undefined;
}

function EditorStatusDisplay({ currentUser, activeEditor }: EditorStatusDisplayProps) {
  const [isLocalEditor, setIsLocalEditor] = useState(activeEditor?.id === currentUser.id);

  // Update local state when activeEditor changes
  useEffect(() => {
    setIsLocalEditor(activeEditor?.id === currentUser.id);
  }, [activeEditor, currentUser.id]);

  const handleBecomeEditor = () => {
    // Request a lock for a dummy voucher to become an editor
    const socket = getSocket();
    if (socket) {
      // Immediately update UI to show as editor (optimistic update)
      setIsLocalEditor(true);

      socket.emit('lock_request', { resourceType: 'voucher', resourceId: 'dummy-' + Date.now() }, (response: any) => {
        if (response.success) {
          console.log('Successfully acquired editor rights');
          // Show success toast
          toast({
            title: "Editor Rights Acquired",
            description: "You now have editing rights for vouchers.",
            variant: "default",
          });
        } else {
          console.error('Failed to acquire editor rights:', response.message);
          // Revert UI if request failed
          setIsLocalEditor(false);

          // Show error toast
          toast({
            title: "Failed to Acquire Editor Rights",
            description: response.message || "Another user may already have editor rights.",
            variant: "destructive",
          });
        }
      });
    }
  };

  return (
    <>
      <Badge
        variant={isLocalEditor ? "default" : "outline"}
        className={isLocalEditor ? "bg-green-600" : "border-yellow-600 text-yellow-600"}
      >
        <UserCheck className="h-3 w-3 mr-1" />
        {isLocalEditor ? "Editor" : "Viewer"}
      </Badge>

      {/* Add button to request editor rights if not already an editor */}
      {!isLocalEditor && (
        <Button
          variant="outline"
          size="sm"
          className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
          onClick={handleBecomeEditor}
        >
          Become Editor
        </Button>
      )}

      {/* Show a green button when user is editor */}
      {isLocalEditor && (
        <Badge variant="default" className="bg-green-600">
          <Lock className="h-3 w-3 mr-1" />
          Active
        </Badge>
      )}
    </>
  );
}

export function ActiveUsersDisplay({ department }: ActiveUsersDisplayProps) {
  const [activeUsers, setActiveUsers] = useState<{id: string, name: string, isActive: boolean}[]>([]);
  const [activeLocks, setActiveLocks] = useState<{userId: string, resourceType: string, key: string}[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const currentUser = useAppStore((state) => state.currentUser);
  const users = useAppStore((state) => state.users);

  // Filter users by department
  const departmentUsers = users.filter(user => user.department === department);

  // Function to refresh user and lock data
  const refreshData = useCallback(async () => {
    const socket = getSocket();
    if (!socket) {
      console.error('Cannot refresh data: Socket not connected');

      // Show toast notification
      if (window.toast) {
        window.toast({
          title: "Connection Error",
          description: "Not connected to server. Please refresh the page.",
          variant: "destructive",
        });
      }
      return;
    }

    setIsRefreshing(true);
    console.log('Refreshing active users and locks data for department:', department);

    try {
      // Request current active users
      socket.emit('get_department_users', { department }, (response: any) => {
        if (response?.success) {
          console.log('Received updated users data:', response.users);
          setActiveUsers(response.users);
        } else {
          console.error('Failed to get department users:', response?.message || 'Unknown error');
        }
      });

      // Request current locks
      socket.emit('get_locks', (response: any) => {
        if (response?.success) {
          const locks = response.locks || [];
          const departmentLocks = locks.filter((lock: any) =>
            lock.department === department &&
            lock.key.startsWith('voucher:')
          );
          console.log('Received updated locks data:', departmentLocks);
          setActiveLocks(departmentLocks);
        } else {
          console.error('Failed to get locks:', response?.message || 'Unknown error');
        }
      });

      // Request connected users for this department
      socket.emit('get_connected_users', { department });

      // Request locks for this department
      socket.emit('get_department_locks', { department });

      // Also fetch users from the API to ensure we have the latest data
      const fetchAllUsers = useAppStore.getState().fetchAllUsers;
      await fetchAllUsers();

      // Update last refreshed timestamp
      setLastUpdated(new Date());

      // Don't show toast notification for successful refresh - it's too annoying
      // Only log to console
      console.log('Active users data has been refreshed');
    } catch (error) {
      console.error('Error refreshing data:', error);

      // Show toast notification for error
      if (window.toast) {
        window.toast({
          title: "Refresh Failed",
          description: "Failed to refresh active users data",
          variant: "destructive",
        });
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [department]);

  // Separate useEffect for presence announcement
  useEffect(() => {
    const socket = getSocket();
    if (!socket || !currentUser) return;

    console.log(`Setting up presence announcer for ${currentUser.name} in ${department}`);

    // Announce presence every 30 seconds to ensure we're counted
    const announcePresence = () => {
      console.log(`Announcing presence for ${currentUser.name} in ${department}`);
      socket.emit('announce_presence', {
        userId: currentUser.id,
        userName: currentUser.name,
        department: department
      });

      // Also explicitly join the department room
      socket.emit('join_department', {
        department: department,
        userId: currentUser.id,
        userName: currentUser.name
      });
    };

    // Call immediately
    announcePresence();

    // Set up interval for periodic announcements
    const presenceInterval = setInterval(announcePresence, 30000);

    // Clean up interval on unmount
    return () => {
      console.log(`Cleaning up presence announcer for ${currentUser.name}`);
      clearInterval(presenceInterval);
    };
  }, [currentUser, department]);

  // Main useEffect for socket listeners
  useEffect(() => {
    const socket = getSocket();
    if (!socket) return;

    // Listen for connected users updates
    const handleConnectedUsers = (data: any) => {
      console.log('Received connected users update:', data);
      if (data.department === department) {
        setActiveUsers(data.users);
        setLastUpdated(new Date());

        // Show toast notification for user status changes
        const currentUserId = currentUser?.id;
        if (currentUserId && window.toast) {
          // Check if there are any new active users
          const newActiveUsers = data.users.filter((user: any) =>
            user.isActive &&
            !activeUsers.some(existingUser => existingUser.id === user.id && existingUser.isActive)
          );

          // Check if any users became inactive
          const newInactiveUsers = activeUsers.filter(existingUser =>
            existingUser.isActive &&
            !data.users.some((user: any) => user.id === existingUser.id && user.isActive)
          );

          // Show notifications for new active users (except current user)
          newActiveUsers
            .filter((user: any) => user.id !== currentUserId)
            .forEach((user: any) => {
              window.toast({
                title: "User Active",
                description: `${user.name} is now active in ${department}`,
                variant: "default",
              });
            });

          // Show notifications for users who became inactive (except current user)
          newInactiveUsers
            .filter(user => user.id !== currentUserId)
            .forEach(user => {
              window.toast({
                title: "User Inactive",
                description: `${user.name} is now inactive in ${department}`,
                variant: "default",
              });
            });
        }
      }
    };

    // Listen for lock updates
    const handleLocksUpdate = (data: any) => {
      console.log('Received locks update:', data);
      const locks = data.locks || [];
      const departmentLocks = locks.filter((lock: any) =>
        lock.department === department &&
        lock.key.startsWith('voucher:')
      );

      // Check for changes in locks
      const previousEditorId = activeLocks.length > 0 ? activeLocks[0].userId : null;
      const newEditorId = departmentLocks.length > 0 ? departmentLocks[0].userId : null;

      // Update locks state
      setActiveLocks(departmentLocks);
      setLastUpdated(new Date());

      // Show toast notification for editor changes
      if (previousEditorId !== newEditorId && window.toast) {
        if (newEditorId) {
          // Someone became an editor
          const editorName = newEditorId === currentUser?.id ? 'You' :
            users.find(u => u.id === newEditorId)?.name || 'Unknown user';

          const resourceId = departmentLocks.length > 0 ?
            departmentLocks[0].key.split(':')[1] : 'unknown';

          window.toast({
            title: "Editor Changed",
            description: `${editorName} now has editing rights for voucher ${resourceId}`,
            variant: "default",
          });
        } else if (previousEditorId) {
          // Someone lost editor status
          window.toast({
            title: "Editor Released",
            description: "All vouchers are now available for editing",
            variant: "default",
          });
        }
      }
    };

    // Listen for department locks updates
    const handleDepartmentLocksUpdate = (data: any) => {
      console.log('Received department locks update:', data);
      if (data.department === department) {
        // Check for changes in locks
        const previousEditorId = activeLocks.length > 0 ? activeLocks[0].userId : null;
        const newLocks = data.locks || [];
        const newEditorId = newLocks.length > 0 ? newLocks[0].userId : null;

        // Update locks state
        setActiveLocks(newLocks);
        setLastUpdated(new Date());

        // Show toast notification for editor changes
        if (previousEditorId !== newEditorId && window.toast) {
          if (newEditorId) {
            // Someone became an editor
            const editorName = newEditorId === currentUser?.id ? 'You' :
              users.find(u => u.id === newEditorId)?.name || 'Unknown user';

            const resourceId = newLocks.length > 0 ?
              newLocks[0].key.split(':')[1] : 'unknown';

            window.toast({
              title: "Editor Changed",
              description: `${editorName} now has editing rights for voucher ${resourceId}`,
              variant: "default",
            });
          } else if (previousEditorId) {
            // Someone lost editor status
            window.toast({
              title: "Editor Released",
              description: "All vouchers are now available for editing",
              variant: "default",
            });
          }
        }
      }
    };

    // Initial data fetch
    refreshData();

    // Set up listeners
    socket.on('connected_users', handleConnectedUsers);
    socket.on('locks_update', handleLocksUpdate);
    socket.on('department_locks', handleDepartmentLocksUpdate);

    // Set up periodic refresh (every 30 seconds)
    const refreshInterval = setInterval(() => {
      refreshData();
    }, 30000);

    // Clean up
    return () => {
      socket.off('connected_users', handleConnectedUsers);
      socket.off('locks_update', handleLocksUpdate);
      socket.off('department_locks', handleDepartmentLocksUpdate);
      clearInterval(refreshInterval);

      // No need to return anything here as the parent useEffect will handle the presence interval cleanup
    };
  }, [department, refreshData, currentUser]);

  // Determine which user has active editing rights
  const activeEditor = activeLocks.length > 0
    ? departmentUsers.find(user => activeLocks.some(lock => lock.userId === user.id))
    : null;

  // Count active users in this department
  // Always include the current user in the count
  const activeUserCount = activeUsers.filter(user => user.isActive).length;

  // If count is 0 but current user is logged in, show at least 1
  const displayCount = activeUserCount === 0 && currentUser ? 1 : activeUserCount;

  // Format last updated time
  const formatLastUpdated = () => {
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) return `${diffSec}s ago`;
    const diffMin = Math.floor(diffSec / 60);
    return `${diffMin}m ago`;
  };

  // Get resource information from locks
  const activeResources = activeLocks.map(lock => {
    const resourceId = lock.key.split(':')[1];
    return {
      id: resourceId,
      type: lock.key.split(':')[0],
      owner: users.find(u => u.id === lock.userId)?.name || 'Unknown'
    };
  });

  return (
    <div className="flex items-center space-x-2 bg-gray-900 rounded-md p-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-1 text-blue-400" />
              <span className="text-sm text-blue-400">{displayCount}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="font-semibold">Active users in {department}</p>
            {activeUsers.length > 0 ? (
              <ul className="mt-1">
                {activeUsers.filter(user => user.isActive).map(user => {
                  const isEditor = activeLocks.some(lock => lock.userId === user.id);
                  return (
                    <li key={user.id} className="text-xs flex items-center">
                      {user.name} {user.id === currentUser?.id ? '(You)' : ''}
                      {isEditor && (
                        <Badge className="ml-2 bg-green-600 text-[10px] py-0 px-1">Editor</Badge>
                      )}
                    </li>
                  );
                })}
              </ul>
            ) : (
              <p className="text-xs italic mt-1">No active users detected</p>
            )}
            <p className="text-xs mt-2 text-gray-400">Updated: {formatLastUpdated()}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {activeEditor && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center">
                <Lock className="h-4 w-4 mr-1 text-green-400" />
                <span className="text-sm text-green-400">
                  {activeEditor.id === currentUser?.id ? 'You' : activeEditor.name}
                </span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p className="font-semibold">
                {activeEditor.id === currentUser?.id
                  ? 'You have active editing rights'
                  : `${activeEditor.name} has active editing rights`}
              </p>
              <p className="text-xs mt-1">Other users have read-only access</p>

              {activeResources.length > 0 && (
                <>
                  <p className="text-xs mt-2 font-semibold">Active resources:</p>
                  <ul className="text-xs">
                    {activeResources.map((resource, idx) => (
                      <li key={idx}>{resource.type}: {resource.id}</li>
                    ))}
                  </ul>
                </>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {currentUser && (
        <div className="flex items-center space-x-2">
          <EditorStatusDisplay
            currentUser={currentUser}
            activeEditor={activeEditor}
          />
        </div>
      )}

      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 rounded-full"
        onClick={refreshData}
        disabled={isRefreshing}
        title="Refresh user status"
      >
        <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
      </Button>
    </div>
  );
}
