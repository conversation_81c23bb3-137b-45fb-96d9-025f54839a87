
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Department, Voucher } from '@/lib/types';
import { VoucherListControls } from '@/components/dashboard/voucher-list-controls';
import { VoucherTabs } from '@/components/dashboard/voucher-tabs';
import { VoucherBatchNotification } from '@/components/dashboard/voucher-batch-notification';
import { useDepartmentData } from '@/hooks/use-department-data';
import { useDepartmentUsers } from '@/hooks/use-department-users';

interface DashboardContentProps {
  department: Department;
  refreshTrigger: number;
  onRefresh: () => void;
  onReceiveVouchers: (voucherIds: string[]) => void;
  selectedVouchers: string[];
  dispatchedBy: string;
  customDispatchName: string;
  onDispatcherChange: (value: string) => void;
  onCustomDispatchNameChange: (value: string) => void;
  onSendToAudit: () => void;
  onSelectionChange: (selectedIds: string[]) => void;
  onViewVoucher: (voucher: Voucher) => void;
  voucherView: string;
  onVoucherViewChange: (view: string) => void;
  isNotificationBlinking: boolean;
}

export function DashboardContent({
  department,
  refreshTrigger,
  onRefresh,
  onReceiveVouchers,
  selectedVouchers,
  dispatchedBy,
  customDispatchName,
  onDispatcherChange,
  onCustomDispatchNameChange,
  onSendToAudit,
  onSelectionChange,
  onViewVoucher,
  voucherView,
  onVoucherViewChange,
  isNotificationBlinking
}: DashboardContentProps) {
  const {
    pendingSubmissionVouchers,
    processingVouchers,
    certifiedVouchers,
    rejectedVouchers,
    returnedVouchers,
    batchesArray
  } = useDepartmentData(department, refreshTrigger);

  const departmentUsers = useDepartmentUsers(department);

  const hasVouchersToReceive = batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers.some(v =>
        v.certifiedBy ||
        v.status === "VOUCHER REJECTED" ||
        v.isReturned ||
        v.pendingReturn
      )
    );

  const handleReceiveAllVouchers = () => {
    const allVoucherIds = batchesArray
      .flatMap(batch => batch.vouchers)
      .map(v => v.id);

    console.log("Receiving all vouchers:", allVoucherIds);
    console.log("Voucher statuses:", allVoucherIds.map(id => {
      const v = batchesArray.flatMap(batch => batch.vouchers).find(v => v.id === id);
      return `${id}: status=${v?.status}, isReturned=${v?.isReturned}, pendingReturn=${v?.pendingReturn}`;
    }));

    onReceiveVouchers(allVoucherIds);
  };

  return (
    <main className="flex-1 flex flex-col overflow-hidden bg-black">
      <div className="px-6 pb-6 flex flex-col h-full">
        <div className="flex-1 flex flex-col min-h-0">
          <div className="mb-2">
            <VoucherBatchNotification
              batchesArray={batchesArray}
              onReceiveVouchers={handleReceiveAllVouchers}
              isBlinking={isNotificationBlinking}
            />
          </div>

          <Card className="bg-[#0a0a0a] border-gray-800 flex-1 flex flex-col overflow-hidden">
            <CardContent className="p-4 flex flex-col flex-1">
              <VoucherListControls
                voucherView={voucherView}
                selectedVouchers={selectedVouchers}
                dispatchedBy={dispatchedBy}
                customDispatchName={customDispatchName}
                departmentUsers={departmentUsers}
                onSendToAudit={onSendToAudit}
                onDispatcherChange={onDispatcherChange}
                onCustomDispatchNameChange={onCustomDispatchNameChange}
                isDisabled={hasVouchersToReceive}
              />

              <VoucherTabs
                voucherView={voucherView}
                onVoucherViewChange={onVoucherViewChange}
                pendingSubmissionVouchers={pendingSubmissionVouchers}
                processingVouchers={processingVouchers}
                certifiedVouchers={certifiedVouchers}
                rejectedVouchers={rejectedVouchers}
                returnedVouchers={returnedVouchers}
                department={department}
                onSelectionChange={onSelectionChange}
                onViewVoucher={onViewVoucher}
                isDisabled={hasVouchersToReceive}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
