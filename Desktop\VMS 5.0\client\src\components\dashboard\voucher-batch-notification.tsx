
import { ArrowDownCircle, Lock, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Voucher } from '@/lib/types';
import { useEffect, useRef, useState } from 'react';

interface VoucherBatchNotificationProps {
  batchesArray: Array<{
    dispatchTime: string;
    dispatchedBy: string;
    vouchers: Voucher[];
  }>;
  onReceiveVouchers: () => void;
  isBlinking?: boolean;
}

export function VoucherBatchNotification({ 
  batchesArray, 
  onReceiveVouchers,
  isBlinking = false
}: VoucherBatchNotificationProps) {
  const notificationRef = useRef<HTMLDivElement>(null);
  const [blinking, setBlinking] = useState(isBlinking);
  
  // Use effect to handle blinking state and scrolling
  useEffect(() => {
    setBlinking(isBlinking);
    
    if (isBlinking && notificationRef.current) {
      notificationRef.current.scrollIntoView({ behavior: 'smooth' });
      
      // Stop blinking after 3 seconds
      const timer = setTimeout(() => {
        setBlinking(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [isBlinking]);
  
  // Early return if there are no batches to display
  if (!batchesArray || batchesArray.length === 0) return null;
  
  // Count total vouchers that are coming FROM Audit across all batches
  const totalVouchers = batchesArray.reduce(
    (total, batch) => total + batch.vouchers.length, 0
  );
  
  // Only display if there are vouchers coming back FROM Audit
  if (totalVouchers === 0) return null;
  
  // Verify these are only vouchers coming FROM Audit (already processed)
  // Check if any voucher has certifiedBy or is rejected - that means it came from Audit
  const hasProcessedVouchers = batchesArray.some(batch => 
    batch.vouchers.some(v => v.certifiedBy || v.status === "VOUCHER REJECTED" || v.isReturned || v.pendingReturn)
  );
  
  if (!hasProcessedVouchers) return null;

  // Count returned vouchers specifically
  const returnedVoucherCount = batchesArray.reduce(
    (count, batch) => count + batch.vouchers.filter(v => v.isReturned || v.pendingReturn).length, 0
  );

  // Determine notification message based on voucher types
  let notificationMessage = "YOU HAVE NEW VOUCHER FROM AUDIT: RECEIVE TO PROCEED.";
  if (returnedVoucherCount > 0) {
    if (returnedVoucherCount === totalVouchers) {
      notificationMessage = "YOU HAVE RETURNED VOUCHER FROM AUDIT: RECEIVE TO PROCEED.";
    } else {
      notificationMessage = "YOU HAVE NEW AND RETURNED VOUCHERS FROM AUDIT: RECEIVE TO PROCEED.";
    }
  }

  return (
    <div 
      ref={notificationRef}
      className={`bg-amber-900/20 p-4 rounded-lg mb-6 border ${blinking ? 'animate-pulse border-amber-400' : 'border-amber-700/50'}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {returnedVoucherCount > 0 ? (
            <RefreshCw className={`h-5 w-5 ${blinking ? 'text-amber-400' : 'text-amber-500'}`} />
          ) : (
            <ArrowDownCircle className={`h-5 w-5 ${blinking ? 'text-amber-400' : 'text-amber-500'}`} />
          )}
          <div className="flex items-center">
            <p className={`text-sm ${blinking ? 'text-amber-200 font-semibold' : 'text-amber-300'}`}>
              {notificationMessage}
            </p>
            <Lock className={`ml-2 h-4 w-4 ${blinking ? 'text-amber-400' : 'text-amber-500'}`} />
          </div>
        </div>
        <Button size="sm" onClick={onReceiveVouchers} className="uppercase bg-amber-700 hover:bg-amber-800">
          Receive Vouchers
        </Button>
      </div>
    </div>
  );
}
