import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { useDashboardState } from '@/hooks/use-dashboard-state';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { NewVoucherForm } from '@/components/dashboard/new-voucher-form';
import { DashboardContent } from '@/components/dashboard/dashboard-content';
import { DashboardModals } from '@/components/dashboard/dashboard-modals';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { Department } from '@/lib/types';
import { useDepartmentData } from '@/hooks/use-department-data';
import { useEffect } from 'react';

interface GenericDepartmentDashboardProps {
  department: Department;
}

export function GenericDepartmentDashboard({ department }: GenericDepartmentDashboardProps) {
  const navigate = useNavigate();
  const {
    currentUser,
    selectedVouchers,
    setSelectedVouchers,
    showVoucherReceiving,
    setShowVoucherReceiving,
    receivingVoucherIds,
    setReceivingVoucherIds,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    viewingVoucher,
    setViewingVoucher,
    showBatchReceiving,
    setShowBatchReceiving,
    selectedBatchId,
    setSelectedBatchId,
    voucherView,
    setVoucherView,
    isNotificationBlinking,
    refreshTrigger,
    refreshData,
    handleDisabledFormClick
  } = useDashboardState();

  const { batchesArray } = useDepartmentData(department, refreshTrigger);

  // Check if there are vouchers to receive
  const hasVouchersToReceive = batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers.some(v => 
        v.certifiedBy || 
        v.status === "VOUCHER REJECTED" || 
        v.isReturned || 
        v.pendingReturn
      )
    );

  // Redirect if user is not from this department
  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== department) {
      if (currentUser.department === 'AUDIT') {
        navigate('/audit-dashboard');
      } else if (currentUser.department === 'ADMINISTRATOR') {
        navigate('/admin-dashboard');
      } else {
        navigate('/dashboard');
      }
    }
  }, [currentUser, department, navigate]);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      <DashboardHeader />

      <div className="px-6 py-2 bg-black">
        <NewVoucherForm
          department={department}
          isDisabled={hasVouchersToReceive}
          onDisabledClick={handleDisabledFormClick}
          hidden={hasVouchersToReceive}
        />
      </div>

      <DashboardContent
        department={department}
        refreshTrigger={refreshTrigger}
        onRefresh={refreshData}
        onReceiveVouchers={(voucherIds) => {
          setReceivingVoucherIds(voucherIds);
          setShowVoucherReceiving(true);
        }}
        selectedVouchers={selectedVouchers}
        dispatchedBy={dispatchedBy}
        customDispatchName={customDispatchName}
        onDispatcherChange={setDispatchedBy}
        onCustomDispatchNameChange={setCustomDispatchName}
        onSendToAudit={() => {
          const sendVouchersToAudit = useAppStore.getState().sendVouchersToAudit;

          if (selectedVouchers.length > 0 && (dispatchedBy || customDispatchName)) {
            const finalDispatchedBy = dispatchedBy || customDispatchName.toUpperCase();
            sendVouchersToAudit(department, selectedVouchers, finalDispatchedBy);

            setSelectedVouchers([]);
            setDispatchedBy('');
            setCustomDispatchName('');
            setVoucherView('processing');
          }
        }}
        onSelectionChange={setSelectedVouchers}
        onViewVoucher={setViewingVoucher}
        voucherView={voucherView}
        onVoucherViewChange={setVoucherView}
        isNotificationBlinking={isNotificationBlinking}
      />

      <DashboardModals
        department={department}
        showVoucherReceiving={showVoucherReceiving}
        setShowVoucherReceiving={setShowVoucherReceiving}
        receivingVoucherIds={receivingVoucherIds}
        viewingVoucher={viewingVoucher}
        setViewingVoucher={setViewingVoucher}
        showBatchReceiving={showBatchReceiving}
        setShowBatchReceiving={setShowBatchReceiving}
        selectedBatchId={selectedBatchId}
        onRefresh={refreshData}
      />

      <DashboardFooter />
    </div>
  );
}
