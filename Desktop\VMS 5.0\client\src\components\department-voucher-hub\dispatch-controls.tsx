import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Department } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { formatCurrentDate } from '@/lib/store/utils';

interface DispatchControlsProps {
  pendingDispatchVouchersCount: number;
  dispatchedBy: string;
  customDispatchName: string;
  selectedVouchers: string[];
  setDispatchedBy: (value: string) => void;
  setCustomDispatchName: (value: string) => void;
  handleSendToDepartment: () => void;
  department: Department;
}

export function DispatchControls({
  pendingDispatchVouchersCount,
  dispatchedBy,
  customDispatchName,
  selectedVouchers,
  setDispatchedBy,
  setCustomDispatchName,
  handleSendToDepartment,
  department,
}: DispatchControlsProps) {
  const selectedCount = selectedVouchers.length;
  const users = useAppStore((state) => state.users);
  // When in Audit side, we should show Audit users regardless of the department we're viewing
  const departmentUsers = users.filter(user => user.department === "AUDIT").map(user => user.name);
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const vouchers = useAppStore((state) => state.vouchers);

  const handleDispatchByChange = (value: string) => {
    setDispatchedBy(value);

    // Only update if a valid selection is made and there are selected vouchers
    if (value !== 'NO_SELECTION' && value !== 'OTHER' && selectedVouchers.length > 0) {
      const currentTime = formatCurrentDate();

      // Update each selected voucher
      selectedVouchers.forEach(voucherId => {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          console.log(`Updating voucher ${voucherId} with dispatch info:`, {
            dispatchedBy: value,
            dispatchTime: currentTime
          });

          updateVoucher(voucherId, {
            dispatchedBy: value,
            dispatchTime: currentTime
          });
        }
      });
    }
  };

  const handleCustomNameChange = (value: string) => {
    const upperValue = value.toUpperCase();
    setCustomDispatchName(upperValue);

    // Only update if there's a value and selected vouchers
    if (upperValue.trim() && selectedVouchers.length > 0) {
      const currentTime = formatCurrentDate();

      // Update each selected voucher
      selectedVouchers.forEach(voucherId => {
        const voucher = vouchers.find(v => v.id === voucherId);
        if (voucher) {
          console.log(`Updating voucher ${voucherId} with custom dispatch info:`, {
            dispatchedBy: upperValue,
            dispatchTime: currentTime
          });

          updateVoucher(voucherId, {
            dispatchedBy: upperValue,
            dispatchTime: currentTime
          });
        }
      });
    }
  };

  return (
    <div className="mb-4 flex flex-col sm:flex-row gap-4 items-center justify-between border-b pb-4">
      <div className="flex flex-col w-full sm:w-auto">
        <Label htmlFor="dispatch-person" className="mb-2">SELECT AUDIT OFFICER DISPATCHING THESE VOUCHERS</Label>
        <div className="flex gap-2">
          <Select value={dispatchedBy} onValueChange={handleDispatchByChange}>
            <SelectTrigger className="w-full sm:w-56 uppercase">
              <SelectValue placeholder="SELECT PERSON" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="NO_SELECTION" className="uppercase">SELECT PERSON</SelectItem>
              {departmentUsers.map(user => (
                <SelectItem key={user} value={user} className="uppercase">{user}</SelectItem>
              ))}
              <SelectItem value="OTHER" className="uppercase">OTHER</SelectItem>
            </SelectContent>
          </Select>

          {dispatchedBy === "OTHER" && (
            <Input
              placeholder="ENTER NAME"
              className="uppercase"
              value={customDispatchName}
              onChange={(e) => handleCustomNameChange(e.target.value)}
            />
          )}
        </div>
      </div>

      <Button
        size="lg"
        onClick={handleSendToDepartment}
        disabled={selectedCount === 0 || (dispatchedBy === "NO_SELECTION" && !customDispatchName)}
        className="w-full sm:w-auto mt-4 sm:mt-0 uppercase"
      >
        SEND VOUCHERS TO DEPARTMENT ({selectedCount})
      </Button>
    </div>
  );
}
