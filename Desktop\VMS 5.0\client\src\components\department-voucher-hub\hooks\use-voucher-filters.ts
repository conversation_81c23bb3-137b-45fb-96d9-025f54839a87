
import { useState } from 'react';
import { Voucher } from '@/lib/types';
import { filterVouchers } from '@/utils/voucherUtils';

export const useVoucherFilters = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };
  
  const getFilteredVouchers = (vouchersToFilter: Voucher[]) => {
    return filterVouchers(vouchersToFilter, searchTerm, sortColumn, sortDirection);
  };
  
  return {
    searchTerm,
    setSearchTerm,
    sortColumn,
    sortDirection,
    handleSort,
    getFilteredVouchers
  };
};
