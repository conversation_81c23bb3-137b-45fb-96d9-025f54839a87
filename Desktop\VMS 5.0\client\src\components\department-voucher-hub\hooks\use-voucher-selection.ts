
import { useState } from 'react';

export const useVoucherSelection = () => {
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([]);
  
  const handleSelectVoucher = (voucherId: string) => {
    setSelectedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId);
      } else {
        return [...prev, voucherId];
      }
    });
  };
  
  const handleSelectAllVouchers = (voucherIds: string[]) => {
    if (voucherIds.length === 0) return;
    
    if (selectedVouchers.length === voucherIds.length) {
      setSelectedVouchers([]);
    } else {
      setSelectedVouchers(voucherIds);
    }
  };
  
  return {
    selectedVouchers,
    setSelectedVouchers,
    handleSelectVoucher,
    handleSelectAllVouchers
  };
};
