
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Auto-refresh vouchers every 5 seconds
  useEffect(() => {
    console.log(`Setting up auto-refresh for ${department} vouchers`);

    // Fetch vouchers immediately
    fetchVouchers(department);

    // Set up interval to refresh every 5 seconds
    const intervalId = setInterval(() => {
      console.log(`Auto-refreshing vouchers for ${department}`);
      fetchVouchers(department);
    }, 5000);

    // Clean up interval on unmount or department change
    return () => {
      console.log(`Cleaning up auto-refresh for ${department}`);
      clearInterval(intervalId);
    };
  }, [department, fetchVouchers]);

  // Filter vouchers for each tab
  const newVouchers = vouchers.filter(v => {
    const isNewVoucher = (
      v.department === department &&
      v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
      v.receivedBy &&
      !v.preAuditedAmount &&
      !v.preAuditedBy &&
      !v.certifiedBy &&
      !v.dispatched &&
      !v.pendingReturn &&
      !v.isReturned
    );

    if (isNewVoucher) {
      console.log(`Voucher ${v.id} (${v.voucherId}) is in NEW VOUCHERS tab`);
    }

    return isNewVoucher;
  });

  // Add debugging for pending dispatch vouchers
  console.log(`Filtering pending dispatch vouchers for ${department}:`,
    vouchers.filter(v => v.department === department && v.status === VOUCHER_STATUSES.AUDIT_PROCESSING)
      .map(v => `${v.voucherId} (${v.id}): preAuditedAmount=${v.preAuditedAmount}, dispatched=${v.dispatched}, pendingReturn=${v.pendingReturn}`)
  );

  const pendingDispatchVouchers = vouchers.filter(v => {
    // Check if this voucher should be in the pending dispatch tab
    const isPendingDispatch = (
      v.department === department &&
      v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
      v.receivedBy &&
      !v.dispatched && // Must not be dispatched
      // Any of these conditions qualifies it for pending dispatch
      (v.preAuditedAmount !== undefined ||
       v.pendingReturn === true ||
       v.preAuditedBy ||
       v.certifiedBy ||
       v.postProvisionalCash)
    );

    if (isPendingDispatch) {
      console.log(`Voucher ${v.id} (${v.voucherId}) is in PENDING DISPATCH tab:`, {
        preAuditedAmount: v.preAuditedAmount,
        preAuditedBy: v.preAuditedBy,
        certifiedBy: v.certifiedBy,
        pendingReturn: v.pendingReturn,
        postProvisionalCash: v.postProvisionalCash
      });
    }

    return isPendingDispatch;
  });

  console.log(`Found ${pendingDispatchVouchers.length} pending dispatch vouchers for ${department}`);

  const dispatchedVouchers = vouchers.filter(v =>
    v.department === department &&
    (v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED ||
     v.status === VOUCHER_STATUSES.VOUCHER_REJECTED ||
     v.status === VOUCHER_STATUSES.VOUCHER_RETURNED) &&
    v.dispatched &&
    !v.deleted
  );

  // Add debugging for returned vouchers
  console.log(`Filtering returned vouchers for ${department}:`,
    vouchers.filter(v => v.isReturned).map(v => `${v.voucherId} (${v.id}): department=${v.department}, isReturned=${v.isReturned}, status=${v.status}, deleted=${v.deleted}, dispatched=${v.dispatched}`)
  );

  const returnedVouchers = vouchers.filter(v =>
    v.department === department &&
    v.isReturned === true &&
    v.status === VOUCHER_STATUSES.VOUCHER_RETURNED &&
    !v.deleted
  );

  console.log(`Found ${returnedVouchers.length} returned vouchers for ${department}`);

  const rejectedVouchers = vouchers.filter(v =>
    v.department === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_REJECTED &&
    v.rejectionTime &&
    !v.deleted
  );

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
