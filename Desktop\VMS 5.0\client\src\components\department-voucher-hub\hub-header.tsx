
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CardHeader, CardTitle } from '@/components/ui/card';
import { Department } from '@/lib/types';

interface HubHeaderProps {
  department: Department;
  onBackToHubs?: () => void;
}

export function HubHeader({ department, onBackToHubs }: HubHeaderProps) {
  return (
    <CardHeader className="pb-3">
      <div className="flex justify-between items-center">
        <CardTitle className="uppercase text-xl">{department} VOUCHER HUB</CardTitle>
        {onBackToHubs && (
          <Button
            variant="outline"
            size="sm"
            onClick={onBackToHubs}
            className="gap-1 border-primary hover:bg-primary/10 text-primary"
          >
            <ArrowLeft className="h-4 w-4" />
            BACK TO HUBS
          </Button>
        )}
      </div>
    </CardHeader>
  );
}
