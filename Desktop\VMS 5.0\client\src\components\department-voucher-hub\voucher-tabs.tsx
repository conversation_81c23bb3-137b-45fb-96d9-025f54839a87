
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Department, Voucher } from '@/lib/types';

interface VoucherTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  department: Department;
  newVouchers: Voucher[];
  pendingDispatchVouchers: Voucher[];
  dispatchedVouchers: Voucher[];
  returnedVouchers: Voucher[];
  rejectedVouchers: Voucher[];
}

export function VoucherTabs({
  activeTab,
  setActiveTab,
  department,
  newVouchers = [],
  pendingDispatchVouchers = [],
  dispatchedVouchers = [],
  returnedVouchers = [],
  rejectedVouchers = []
}: VoucherTabsProps) {
  const tabs = [
    {
      id: 'new-vouchers',
      label: 'NEW VOUCHERS',
      count: newVouchers.length
    },
    {
      id: 'pending-dispatch',
      label: 'PENDING DISPATCH',
      count: pendingDispatchVouchers.length
    },
    {
      id: 'dispatched',
      label: 'DISPATCHED',
      count: dispatchedVouchers.length
    },
    {
      id: 'returned-vouchers',
      label: 'RETURNED',
      count: returnedVouchers.length
    },
    {
      id: 'rejected-vouchers',
      label: 'REJECTED',
      count: rejectedVouchers.length
    }
  ];

  return (
    <Tabs defaultValue="new-vouchers" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-5">
        {tabs.map(tab => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className="whitespace-nowrap text-xs px-2 py-1 sm:text-sm sm:px-4 sm:py-2"
          >
            {tab.label} ({tab.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
