import React from 'react';

interface LogoSignatureProps {
  variant?: 'light' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LogoSignature({
  variant = 'light',
  size = 'md',
  className = ''
}: LogoSignatureProps) {
  // Size classes
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  // Color classes
  const colorClasses = {
    light: 'text-gray-400',
    dark: 'text-gray-600'
  };

  return (
    <div className={`flex flex-col items-center justify-center ${colorClasses[variant]} ${sizeClasses[size]} ${className}`}>
      <div className="font-bold tracking-wider">TEMPLAR SYSTEMS</div>
      <div className="text-xs opacity-80">CREATED BY: SAMUEL ASIEDU</div>
    </div>
  );
}
