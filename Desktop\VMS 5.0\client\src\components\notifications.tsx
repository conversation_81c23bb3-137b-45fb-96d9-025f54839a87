
import { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/store';
import { useNavigate } from 'react-router-dom';

export function NotificationsMenu() {
  const currentUser = useAppStore((state) => state.currentUser);
  const notifications = useAppStore((state) => 
    currentUser ? state.getNotificationsForUser(currentUser.id) : []
  );
  const markNotificationAsRead = useAppStore((state) => state.markNotificationAsRead);
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  
  // Filter out notifications based on the current user's department
  const filteredNotifications = notifications.filter(n => {
    // For Audit department, only show notifications for batches coming TO audit (not from audit)
    if (currentUser?.department === 'AUDIT' && n.type === 'NEW_BATCH') {
      return !n.fromAudit; // Only show if not from audit
    }
    return true; // Show all other notifications
  });
  
  const unreadCount = filteredNotifications.filter((n) => !n.isRead).length;
  
  // Mark all notifications as read when dropdown is closed
  useEffect(() => {
    if (!isOpen && unreadCount > 0) {
      filteredNotifications
        .filter((n) => !n.isRead)
        .forEach((n) => markNotificationAsRead(n.id));
    }
  }, [isOpen, unreadCount, filteredNotifications, markNotificationAsRead]);
  
  const handleNotificationClick = (notificationId: string, voucherId?: string) => {
    markNotificationAsRead(notificationId);
    
    if (voucherId) {
      if (currentUser?.department === 'AUDIT') {
        navigate(`/audit-dashboard/voucher/${voucherId}`);
      } else {
        navigate(`/dashboard/voucher/${voucherId}`);
      }
    }
    
    setIsOpen(false);
  };
  
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        {filteredNotifications.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            No notifications
          </div>
        ) : (
          <>
            {filteredNotifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={`flex flex-col items-start gap-1 p-3 ${
                  !notification.isRead ? 'bg-muted/50' : ''
                }`}
                onClick={() => handleNotificationClick(notification.id, notification.voucherId)}
              >
                <div className="font-medium">{notification.message}</div>
                <div className="text-xs text-muted-foreground">
                  {notification.timestamp}
                </div>
              </DropdownMenuItem>
            ))}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
