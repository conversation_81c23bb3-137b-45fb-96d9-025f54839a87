import { useState } from 'react';
import { Check, X, CornerDownLeft } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useAppStore } from '@/lib/store';
import { Voucher } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { ScrollArea } from '@/components/ui/scroll-area';
import { VoucherRejectionDialog } from './voucher-rejection-dialog';

interface VoucherBatchReceivingProps {
  batchId: string;
  open: boolean;
  onClose: () => void;
  isEditable?: boolean;
}

export function VoucherBatchReceiving({ batchId, open, onClose, isEditable = true }: VoucherBatchReceivingProps) {
  const vouchers = useAppStore((state) => state.vouchers);
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const receiveVoucherBatch = useAppStore((state) => state.receiveVoucherBatch);

  const batch = voucherBatches.find(b => b.id === batchId);
  const batchVouchers = batch ? vouchers.filter(v => batch.voucherIds.includes(v.id)) : [];

  const [acceptedVouchers, setAcceptedVouchers] = useState<string[]>([]);
  const [rejectedVouchers, setRejectedVouchers] = useState<string[]>([]);
  const [rejectionComments, setRejectionComments] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // For rejection dialog
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [currentVoucherId, setCurrentVoucherId] = useState<string | null>(null);

  if (!batch) return null;

  const handleAcceptVoucher = (voucherId: string) => {
    setAcceptedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId);
      } else {
        // Remove from rejected if it's there
        setRejectedVouchers(prev => prev.filter(id => id !== voucherId));
        return [...prev, voucherId];
      }
    });
  };

  const handleStartRejectVoucher = (voucherId: string) => {
    setCurrentVoucherId(voucherId);
    setRejectionDialogOpen(true);
  };

  const handleConfirmReject = (comment: string) => {
    if (currentVoucherId) {
      // Add to rejected vouchers
      setRejectedVouchers(prev => {
        if (!prev.includes(currentVoucherId)) {
          // Remove from accepted if it's there
          setAcceptedVouchers(prev => prev.filter(id => id !== currentVoucherId));
          return [...prev, currentVoucherId];
        }
        return prev;
      });

      // Save the rejection comment
      setRejectionComments(prev => ({
        ...prev,
        [currentVoucherId]: comment
      }));

      // Close the dialog
      setRejectionDialogOpen(false);
      setCurrentVoucherId(null);
    }
  };

  const handleCompleteBatchReceiving = () => {
    const unprocessedVouchers = batchVouchers.filter(
      v => !acceptedVouchers.includes(v.id) && !rejectedVouchers.includes(v.id)
    );

    if (unprocessedVouchers.length > 0) {
      toast.error('Please accept or reject all vouchers in the batch', {
        duration: 3000,
      });
      return;
    }

    // Verify that all rejected vouchers have comments
    const missingComments = rejectedVouchers.filter(id => !rejectionComments[id] || rejectionComments[id].trim() === '');
    if (missingComments.length > 0) {
      toast.error('Please provide rejection comments for all rejected vouchers', {
        duration: 3000,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Before calling receiveVoucherBatch, add the comments to each rejected voucher
      const finalRejectedVouchers = rejectedVouchers.map(id => {
        const comment = rejectionComments[id];
        return { id, comment };
      });

      // Update the receiveVoucherBatch function to process these comments
      receiveVoucherBatch(
        batchId,
        acceptedVouchers,
        finalRejectedVouchers.map(item => item.id),
        rejectionComments
      );

      toast.success(`Vouchers processed: ${acceptedVouchers.length} accepted, ${rejectedVouchers.length} rejected`, {
        duration: 3000,
      });

      onClose();
    } catch (error) {
      toast.error('Failed to process vouchers', {
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="bg-black text-white border-white/10 max-w-4xl">
          <DialogHeader className="pb-2 shrink-0">
            <DialogTitle className="text-base text-white">Receive Voucher Batch</DialogTitle>
            <DialogDescription className="text-xs text-gray-400">
              Review and process vouchers from {batch.department} Department
            </DialogDescription>
          </DialogHeader>

          <div className="flex justify-between items-center py-1 px-1 shrink-0">
            <div className="text-xs text-gray-400">
              Sent by: {batch.sentBy} on {batch.sentTime}
            </div>
            <div className="flex gap-2">
              <Badge variant="success" className="flex gap-1 items-center text-xs py-0.5">
                <Check className="h-3 w-3" /> {acceptedVouchers.length}
              </Badge>
              <Badge variant="destructive" className="flex gap-1 items-center text-xs py-0.5">
                <X className="h-3 w-3" /> {rejectedVouchers.length}
              </Badge>
              <Badge variant="outline" className="flex gap-1 items-center text-xs py-0.5">
                {batchVouchers.length - acceptedVouchers.length - rejectedVouchers.length}
              </Badge>
            </div>
          </div>

          <div className="overflow-y-auto flex-1 min-h-0">
            <div className="space-y-2 p-1">
              {batchVouchers.map((voucher) => {
                const isAccepted = acceptedVouchers.includes(voucher.id);
                const isRejected = rejectedVouchers.includes(voucher.id);
                const isReturning = voucher.pendingReturn || voucher.isReturned;

                return (
                  <div
                    key={voucher.id}
                    className={`py-3 px-4 mb-2 rounded-lg border border-white/10 ${
                      isAccepted ? 'bg-green-950/20' :
                      isRejected ? 'bg-red-950/20' :
                      isReturning ? 'bg-yellow-950/20' : 'bg-[#0f0f0f]'
                    }`}
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-semibold text-white">{voucher.voucherId}</h3>
                          {isReturning && (
                            <Badge variant="outline" className="bg-yellow-900/30 text-yellow-300 border-yellow-600">
                              <CornerDownLeft className="h-3 w-3 mr-1" /> RETURNING
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-white/70">Claimant: {voucher.claimant}</p>
                        <p className="text-sm text-white/70">Date: {voucher.date}</p>

                        {isReturning && voucher.returnComment && (
                          <p className="text-sm mt-2 bg-yellow-900/20 p-2 border border-yellow-800/30 rounded">
                            <span className="font-medium text-yellow-300">Return Reason:</span> {voucher.returnComment}
                          </p>
                        )}

                        {isRejected && rejectionComments[voucher.id] && (
                          <div className="mt-2 p-2 bg-red-900/20 border border-red-900/30 rounded-md">
                            <p className="text-xs text-red-300 font-medium">REJECTION REASON:</p>
                            <p className="text-sm text-white/90">{rejectionComments[voucher.id]}</p>
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-sm mb-1"><span className="font-medium">Description:</span> {voucher.description}</p>
                        <p className="text-sm font-semibold">Amount: {voucher.amount.toFixed(2)} {voucher.currency}</p>

                        <div className="flex justify-end mt-3 gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className={`${isRejected ? 'bg-red-500 text-white hover:bg-red-600' : ''} px-4`}
                            onClick={() => isRejected ? handleAcceptVoucher(voucher.id) : handleStartRejectVoucher(voucher.id)}
                            disabled={!isEditable}
                          >
                            <X className="h-4 w-4 mr-2" />
                            {isRejected ? 'Undo Reject' : 'Reject'}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            className={`${isAccepted ? 'bg-green-500 text-white hover:bg-green-600' : ''} px-4`}
                            onClick={() => handleAcceptVoucher(voucher.id)}
                            disabled={!isEditable}
                          >
                            <Check className="h-4 w-4 mr-2" />
                            {isAccepted ? 'Undo Accept' : 'Accept'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <DialogFooter className="pt-4 mt-2 border-t border-white/10">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="h-9"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCompleteBatchReceiving}
              disabled={
                isSubmitting ||
                acceptedVouchers.length + rejectedVouchers.length !== batchVouchers.length ||
                !isEditable
              }
              className="h-9 bg-blue-600 hover:bg-blue-700"
            >
              Complete Processing
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection dialog */}
      <VoucherRejectionDialog
        isOpen={rejectionDialogOpen}
        onClose={() => {
          setRejectionDialogOpen(false);
          setCurrentVoucherId(null);
        }}
        onConfirm={handleConfirmReject}
      />
    </>
  );
}
