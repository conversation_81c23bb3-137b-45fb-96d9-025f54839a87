import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
} from '@/components/custom-dialog';
import { X } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from './ui/alert-dialog';
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';

interface VoucherDetailsModalProps {
  voucher: Voucher;
  onClose: () => void;
  showDelete?: boolean;
  onDelete?: (voucherId: string) => void;
  showAddBack?: boolean;
  department?: string;
}

export function VoucherDetailsModal({
  voucher,
  onClose,
  showDelete = false,
  onDelete,
  showAddBack = false,
  department
}: VoucherDetailsModalProps) {
  console.log('Rendering VoucherDetailsModal for voucher:', voucher.voucherId, 'in department:', department);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const updateVoucher = useAppStore((state) => state.updateVoucher);

  useEffect(() => {
    const handleDialogClose = () => {
      onClose();
    };

    document.addEventListener('dialog-close', handleDialogClose);
    return () => {
      document.removeEventListener('dialog-close', handleDialogClose);
    };
  }, [onClose]);

  const formatAmount = (amount: number) => {
    return `${amount.toFixed(2)} ${voucher.currency}`;
  };

  const formatStatus = (status: string) => {
    switch (status) {
      case 'VOUCHER CERTIFIED':
        return <Badge variant="success">CERTIFIED</Badge>;
      case 'VOUCHER REJECTED':
        return <Badge variant="destructive">REJECTED</Badge>;
      case 'AUDIT: PROCESSING':
        return <Badge variant="warning">PROCESSING</Badge>;
      case 'PENDING SUBMISSION':
        return <Badge variant="outline">PENDING SUBMISSION</Badge>;
      case 'PENDING RECEIPT':
        return <Badge variant="outline">PENDING RECEIPT</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleAddBack = () => {
    try {
      console.log(`Adding voucher ${voucher.id} (${voucher.voucherId}) back to pending submission`);

      // APPROACH 1: Preserve the original voucher ID when adding rejected vouchers back to PENDING
      // This maintains traceability and history of the voucher throughout its lifecycle
      // The voucher ID remains in the blacklist for record-keeping, but this doesn't prevent resubmission

      // Update the voucher to move it back to pending submission while preserving its ID
      // Determine the appropriate comment based on the voucher's current status
      const currentDate = new Date().toLocaleDateString();
      const commentText = voucher.status === "VOUCHER REJECTED"
        ? `Re-added from rejection on ${currentDate}`
        : voucher.isReturned || voucher.status === "VOUCHER RETURNED"
          ? `Re-added from returned on ${currentDate}`
          : `Re-added to pending on ${currentDate}`;

      updateVoucher(voucher.id, {
        status: "PENDING SUBMISSION",
        sentToAudit: false,
        isReturned: false,
        returnTime: undefined,
        returnComment: undefined,
        rejectionTime: undefined,
        comment: commentText,
        deleted: false, // Make sure it's not marked as deleted
        // Reset any fields that might prevent it from appearing in the PROCESSING tab
        auditDispatchTime: undefined,
        auditDispatchedBy: undefined,
        dispatched: false,
        dispatchTime: undefined,
        dispatchedBy: undefined,
        pendingReturn: false
      });

      // Create a toast message that reflects the source of the re-addition
      const toastMessage = voucher.status === "VOUCHER REJECTED"
        ? `Voucher ${voucher.voucherId} re-added from rejection to Pending Submission`
        : voucher.isReturned || voucher.status === "VOUCHER RETURNED"
          ? `Voucher ${voucher.voucherId} re-added from returned to Pending Submission`
          : `Voucher ${voucher.voucherId} moved back to Pending Submission`;

      toast({
        title: "Success",
        description: toastMessage,
        duration: 3000,
      });

      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add voucher back",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(voucher.id);
    }
  };

  return (
    <>
      <CustomDialog onClose={onClose}>
        <CustomDialogContent className="max-w-3xl max-h-[90vh] flex flex-col">
          <CustomDialogHeader>
            <CustomDialogTitle className="uppercase">Voucher Details: {voucher.voucherId}</CustomDialogTitle>
            <CustomDialogDescription>
              Viewing details for voucher created on {voucher.date}
            </CustomDialogDescription>
          </CustomDialogHeader>

          <Tabs defaultValue="details" className="flex-1 overflow-hidden flex flex-col">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="details">Voucher Details</TabsTrigger>
              <TabsTrigger value="tracking">Tracking Information</TabsTrigger>
            </TabsList>

            <ScrollArea className="flex-1">
              <TabsContent value="details" className="space-y-4 p-1">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Claimant</h3>
                    <p className="text-sm uppercase">{voucher.claimant}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Department</h3>
                    <p className="text-sm uppercase">{voucher.department}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-semibold uppercase">Description</h3>
                  <p className="text-sm uppercase">{voucher.description}</p>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Amount</h3>
                    <p className="text-sm uppercase">{formatAmount(voucher.amount)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Status</h3>
                    <div className="mt-1">{formatStatus(voucher.status)}</div>
                  </div>
                </div>

                {voucher.preAuditedAmount && (
                  <>
                    <Separator />
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-semibold uppercase">Certified Amount</h3>
                        <p className="text-sm uppercase">{formatAmount(voucher.preAuditedAmount)}</p>
                      </div>
                      {voucher.preAuditedBy && (
                        <div>
                          <h3 className="text-sm font-semibold uppercase">Certified By</h3>
                          <p className="text-sm uppercase">{voucher.preAuditedBy}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}

                {voucher.taxType && (
                  <>
                    <Separator />
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-semibold uppercase">Tax Type</h3>
                        <p className="text-sm uppercase">{voucher.taxType}</p>
                      </div>
                      {voucher.taxAmount && (
                        <div>
                          <h3 className="text-sm font-semibold uppercase">Tax Amount</h3>
                          <p className="text-sm uppercase">{formatAmount(voucher.taxAmount)}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}

                {voucher.comment && (
                  <>
                    <Separator />
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm uppercase">Comment</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm uppercase">{voucher.comment}</p>
                      </CardContent>
                    </Card>
                  </>
                )}
              </TabsContent>

              <TabsContent value="tracking" className="space-y-4 p-1">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm uppercase">Voucher Creation</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Date Created</p>
                        <p className="text-sm uppercase">{voucher.date}</p>
                      </div>
                      <div>
                        <p className="text-xs font-medium uppercase">Created By</p>
                        <p className="text-sm uppercase">{voucher.createdBy || "N/A"}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Department</p>
                        <p className="text-sm uppercase">{voucher.department}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm uppercase">Department Dispatch</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Dispatched By</p>
                        <p className="text-sm uppercase font-semibold">{voucher.dispatchedBy || "NOT YET DISPATCHED"}</p>
                      </div>
                      <div>
                        <p className="text-xs font-medium uppercase">Dispatch Time</p>
                        <p className="text-sm uppercase font-semibold">{voucher.dispatchTime || "NOT YET DISPATCHED"}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {voucher.receiptTime && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Audit Receipt</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Received By</p>
                          <p className="text-sm uppercase">{voucher.receivedBy}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Receipt Time</p>
                          <p className="text-sm uppercase">{voucher.receiptTime}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {voucher.status === "VOUCHER REJECTED" && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Rejection Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Created By</p>
                          <p className="text-sm uppercase">{voucher.createdBy || "N/A"}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Dispatched By</p>
                          <p className="text-sm uppercase">{voucher.dispatchedBy || "N/A"}</p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Rejected By</p>
                          <p className="text-sm uppercase">{voucher.rejectedBy || "N/A"}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Rejection Time</p>
                          <p className="text-sm uppercase">{voucher.rejectionTime || "N/A"}</p>
                        </div>
                      </div>
                      {voucher.comment && (
                        <div>
                          <p className="text-xs font-medium uppercase">Rejection Reason</p>
                          <p className="text-sm uppercase">{voucher.comment}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {voucher.preAuditedBy && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Certification</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Certified By</p>
                          <p className="text-sm uppercase">{voucher.preAuditedBy}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Certified Amount</p>
                          <p className="text-sm uppercase">{voucher.preAuditedAmount ? formatAmount(voucher.preAuditedAmount) : "N/A"}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {voucher.auditDispatchedBy && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Audit Dispatch</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Dispatched By</p>
                          <p className="text-sm uppercase">{voucher.auditDispatchedBy}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Dispatch Time</p>
                          <p className="text-sm uppercase">{voucher.auditDispatchTime}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {voucher.departmentReceivedBy && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Department Receipt</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Received By</p>
                          <p className="text-sm uppercase">{voucher.departmentReceivedBy}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Receipt Time</p>
                          <p className="text-sm uppercase">{voucher.departmentReceiptTime}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </ScrollArea>
          </Tabs>

          <CustomDialogFooter className="flex items-center justify-between gap-2">
            {showAddBack && (
              <Button variant="secondary" onClick={handleAddBack}>
                Add Back to Pending
              </Button>
            )}
            {showDelete && (
              <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
                Delete Voucher
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => {
                onClose();
              }}
            >
              Close
            </Button>
          </CustomDialogFooter>
        </CustomDialogContent>
      </CustomDialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={(open) => setIsDeleteDialogOpen(open)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this voucher?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the voucher from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
