import { useState } from 'react';
import { formatCurrentDate } from '@/utils/formatUtils';
import { Department, Currency } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useAppStore } from '@/lib/store';
import { currencies } from '@/lib/data';
import { toast } from 'sonner';
import { AlertCircle } from 'lucide-react';

interface VoucherFormProps {
  department: Department;
  onSubmit?: () => void;
  onComplete?: () => void;
  isDisabled?: boolean;
  infoMessage?: string;
}

export function VoucherForm({
  department,
  onSubmit,
  onComplete,
  isDisabled = false,
  infoMessage
}: VoucherFormProps) {
  const currentUser = useAppStore((state) => state.currentUser);
  const addVoucher = useAppStore((state) => state.addVoucher);

  const [claimant, setClaimant] = useState('');
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState<Currency>("GHS");
  const [createdVoucherId, setCreatedVoucherId] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isDisabled) {
      return;
    }

    if (!claimant || !description || !amount) {
      toast.error('Please fill all required fields', {
        duration: 3000,
      });
      return;
    }

    if (isNaN(Number(amount)) || Number(amount) <= 0) {
      toast.error('Amount must be a positive number', {
        duration: 3000,
      });
      return;
    }

    const amountValue = parseFloat(amount);

    const date = formatCurrentDate();

    try {
      const newVoucher = await addVoucher({
        date,
        claimant,
        description,
        amount: amountValue,
        currency,
        department,
      });

      // Store the voucher ID to display it
      if (newVoucher && newVoucher.voucher_id) {
        setCreatedVoucherId(newVoucher.voucher_id);
      }

      toast.success(`Voucher ${newVoucher?.voucher_id || ''} created successfully. It is pending submission to Audit.`, {
        duration: 5000,
      });

      // Reset form fields but keep the voucher ID displayed
      setClaimant('');
      setDescription('');
      setAmount('');
      setCurrency("GHS");

      if (onSubmit) {
        onSubmit();
      }

      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      toast.error('Failed to create voucher', {
        duration: 3000,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      {infoMessage && (
        <div className="mx-4 mt-2 p-2 bg-amber-900/30 border border-amber-700/50 rounded flex items-center text-amber-300 text-sm">
          <AlertCircle className="h-4 w-4 mr-2 text-amber-400" />
          {infoMessage}
        </div>
      )}

      <CardContent className="p-4 pb-0">
        {createdVoucherId && (
          <div className="mb-4 p-3 bg-green-900/30 border border-green-700/50 rounded flex items-center">
            <div className="text-green-300 text-sm">
              <span className="font-semibold">Voucher Created: </span>
              <span className="bg-green-950/50 px-2 py-1 rounded font-mono">{createdVoucherId}</span>
              <p className="mt-1 text-xs text-green-400">Your voucher has been created and is ready to be sent to Audit.</p>
            </div>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-1">
            <Label htmlFor="claimant" className="text-xs">CLAIMANT</Label>
            <Input
              id="claimant"
              value={claimant}
              onChange={(e) => setClaimant(e.target.value)}
              placeholder="Enter claimant name"
              required
              className="h-9 text-sm"
              disabled={isDisabled}
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="amount" className="text-xs">AMOUNT</Label>
            <div className="flex gap-2">
              <Input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Enter amount"
                min="0"
                step="0.01"
                required
                className="flex-1 h-9 text-sm"
                disabled={isDisabled}
              />
              <Select
                value={currency}
                onValueChange={(value) => setCurrency(value as Currency)}
                defaultValue="GHS"
                disabled={isDisabled}
              >
                <SelectTrigger className="w-[80px] h-9 text-sm">
                  <SelectValue placeholder="Currency" />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((c) => (
                    <SelectItem key={c} value={c}>{c}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-1 mb-4">
          <Label htmlFor="description" className="text-xs">DESCRIPTION</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter voucher description"
            required
            className="h-20 text-sm min-h-[60px]"
            disabled={isDisabled}
          />
        </div>
      </CardContent>
      <CardFooter className="pt-0 pb-2">
        <Button
          type="submit"
          className="w-40 h-8 text-sm"
          disabled={isDisabled}
        >
          Create Voucher
        </Button>
      </CardFooter>
    </form>
  );
}
