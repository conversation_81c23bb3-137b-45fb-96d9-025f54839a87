import React, { useState, useRef, useEffect } from 'react';
import { formatNumberWithCommas } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Voucher } from '@/lib/types';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader } from '@/components/ui/dialog';
import { ArrowUpDown, Download, Filter } from 'lucide-react';
import { exportVouchersToExcel, groupVouchersByMonth } from '@/utils/exportUtils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface CertifiedVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  onViewVoucher?: (voucher: Voucher) => void;
}

export function CertifiedVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  onViewVoucher,
}: CertifiedVouchersTabProps) {
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string>('All');

  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Filter vouchers by selected month
  const vouchersByMonth = groupVouchersByMonth(filteredVouchers);

  // Get all months that have vouchers
  const availableMonths = Object.entries(vouchersByMonth)
    .filter(([_, vouchers]) => vouchers.length > 0)
    .map(([month]) => month);

  // Get the vouchers for the selected month or all vouchers if 'All' is selected
  const displayedVouchers = selectedMonth === 'All'
    ? filteredVouchers
    : vouchersByMonth[selectedMonth] || [];

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);

  const handleViewVoucher = (voucher: Voucher) => {
    if (onViewVoucher) {
      onViewVoucher(voucher);
    } else {
      setSelectedVoucher(voucher);
    }
  };

  const handleCloseDialog = () => {
    setSelectedVoucher(null);
  };

  // Handle export to Excel
  const handleExport = () => {
    exportVouchersToExcel(
      displayedVouchers,
      `certified-vouchers-${selectedMonth.toLowerCase() !== 'all' ? selectedMonth.toLowerCase() + '-' : ''}${new Date().toISOString().split('T')[0]}`
    );
  };

  return (
    <div className="space-y-2">
      {/* Controls for filtering and export */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">Filter by Month:</span>
          <Select value={selectedMonth} onValueChange={setSelectedMonth}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Month" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All Months</SelectItem>
              {availableMonths.map(month => (
                <SelectItem key={month} value={month}>{month}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="flex items-center space-x-2"
          onClick={handleExport}
        >
          <Download className="h-4 w-4" />
          <span>Export to Excel</span>
        </Button>
      </div>

      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <thead>
                <tr className="bg-background">
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('voucherId')}
                    >
                      <span>VOUCHER ID</span>
                      {sortColumn === 'voucherId' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('date')}
                    >
                      <span>DATE</span>
                      {sortColumn === 'date' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('claimant')}
                    >
                      <span>CLAIMANT</span>
                      {sortColumn === 'claimant' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('description')}
                    >
                      <span>DESCRIPTION</span>
                      {sortColumn === 'description' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('amount')}
                    >
                      <span>AMOUNT</span>
                      {sortColumn === 'amount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedAmount')}
                    >
                      <span>CERTIFIED AMT</span>
                      {sortColumn === 'preAuditedAmount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">TAX</th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">CERTIFIED BY</th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">DISPATCHED BY</th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">DISPATCH TIME</th>
                </tr>
              </thead>
            </table>
          </div>
        </div>

        {/* Scrollable body */}
        <div className="overflow-auto h-[60vh] scrollbar-visible" style={{ scrollbarWidth: 'thin', overflowY: 'scroll' }}>
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <tbody>
              {displayedVouchers.length === 0 ? (
                <tr className="border-b h-14">
                  <td colSpan={10} className="p-4 text-center uppercase font-medium">
                    {selectedMonth !== 'All'
                      ? `NO CERTIFIED VOUCHERS FOUND FOR ${selectedMonth.toUpperCase()}.`
                      : 'NO CERTIFIED VOUCHERS FOUND.'}
                  </td>
                </tr>
              ) : (
                displayedVouchers.map((voucher) => (
                  <tr
                    key={voucher.id}
                    className="cursor-pointer hover:bg-muted/50 border-b h-14"
                    onClick={() => handleViewVoucher(voucher)}
                  >
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.voucherId}</td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">{voucher.date}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.claimant}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.claimant}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.description}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      {formatNumberWithCommas(voucher.amount)} {voucher.currency}
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.taxType && voucher.taxAmount
                                ? `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}`
                                : (voucher.taxType || '-')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {voucher.taxType && voucher.taxAmount
                                ? `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}`
                                : (voucher.taxType || '-')}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.certifiedBy || '-'}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.auditDispatchedBy || '-'}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.auditDispatchTime || '-'}</td>
                  </tr>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Voucher details dialog */}
      {selectedVoucher && (
        <Dialog open={!!selectedVoucher} onOpenChange={handleCloseDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Voucher Details</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4 py-4">
              <div>
                <p className="font-semibold">Voucher ID:</p>
                <p>{selectedVoucher.voucherId}</p>
              </div>
              <div>
                <p className="font-semibold">Date:</p>
                <p>{selectedVoucher.date}</p>
              </div>
              <div>
                <p className="font-semibold">Claimant:</p>
                <p>{selectedVoucher.claimant}</p>
              </div>
              <div>
                <p className="font-semibold">Status:</p>
                <p>{selectedVoucher.status}</p>
              </div>
              <div className="col-span-2">
                <p className="font-semibold">Description:</p>
                <p>{selectedVoucher.description}</p>
              </div>
              <div>
                <p className="font-semibold">Amount:</p>
                <p>{formatNumberWithCommas(selectedVoucher.amount)} {selectedVoucher.currency}</p>
              </div>
              <div>
                <p className="font-semibold">Certified Amount:</p>
                <p>{selectedVoucher.preAuditedAmount ? `${formatNumberWithCommas(selectedVoucher.preAuditedAmount)} ${selectedVoucher.currency}` : '-'}</p>
              </div>
              <div>
                <p className="font-semibold">Tax:</p>
                <p>{selectedVoucher.taxType && selectedVoucher.taxAmount
                  ? `${selectedVoucher.taxType}: ${formatNumberWithCommas(selectedVoucher.taxAmount)} ${selectedVoucher.currency}`
                  : (selectedVoucher.taxType || '-')}</p>
              </div>
              <div>
                <p className="font-semibold">Department:</p>
                <p>{selectedVoucher.department}</p>
              </div>
              <div>
                <p className="font-semibold">Certified By:</p>
                <p>{selectedVoucher.certifiedBy || '-'}</p>
              </div>
              <div>
                <p className="font-semibold">Dispatched By:</p>
                <p>{selectedVoucher.auditDispatchedBy || '-'}</p>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
