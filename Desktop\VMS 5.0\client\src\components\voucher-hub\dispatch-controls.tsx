
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

interface DispatchControlsProps {
  pendingDispatchVouchersCount: number;
  dispatchedBy: string;
  customDispatchName: string;
  auditUsers: string[];
  setDispatchedBy: React.Dispatch<React.SetStateAction<string>>;
  setCustomDispatchName: React.Dispatch<React.SetStateAction<string>>;
  handleSendToDepartment: () => void;
}

export function DispatchControls({
  pendingDispatchVouchersCount,
  dispatchedBy,
  customDispatchName,
  auditUsers,
  setDispatchedBy,
  setCustomDispatchName,
  handleSendToDepartment,
}: DispatchControlsProps) {
  return (
    <div className="mt-4 flex flex-col sm:flex-row gap-4 items-center justify-between border-t pt-4">
      <div className="flex flex-col w-full sm:w-auto">
        <Label htmlFor="dispatch-person" className="mb-2">SELECT WHO IS DISPATCHING THESE VOUCHERS</Label>
        <div className="flex gap-2">
          <Select value={dispatchedBy} onValueChange={setDispatchedBy}>
            <SelectTrigger className="w-full sm:w-56 uppercase">
              <SelectValue placeholder="SELECT PERSON" />
            </SelectTrigger>
            <SelectContent>
              {auditUsers.map(user => (
                <SelectItem key={user} value={user} className="uppercase">{user}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Input
            placeholder="OR ENTER CUSTOM NAME"
            className="uppercase"
            value={customDispatchName}
            onChange={(e) => setCustomDispatchName(e.target.value.toUpperCase())}
          />
        </div>
      </div>
      
      <Button 
        size="lg" 
        onClick={handleSendToDepartment}
        disabled={pendingDispatchVouchersCount === 0 || (!dispatchedBy && !customDispatchName)}
        className="w-full sm:w-auto mt-4 sm:mt-0 uppercase"
      >
        SEND VOUCHERS TO DEPARTMENT
      </Button>
    </div>
  );
}
