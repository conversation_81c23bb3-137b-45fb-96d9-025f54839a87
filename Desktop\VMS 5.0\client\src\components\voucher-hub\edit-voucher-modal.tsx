import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Voucher, TaxType } from '@/lib/types';
import { formatNumberWithCommas } from '@/utils/formatUtils';
import { toast } from 'sonner';

interface EditVoucherModalProps {
  voucher: Voucher;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onSave: (voucherId: string, edits: Partial<Voucher>) => void;
  auditUsers: string[];
}

export function EditVoucherModal({
  voucher,
  isOpen,
  setIsOpen,
  onSave,
  auditUsers
}: EditVoucherModalProps) {
  const [voucherEdits, setVoucherEdits] = useState<Partial<Voucher>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset the state when the modal is opened with a new voucher
  useEffect(() => {
    if (isOpen && voucher) {
      console.log(`Modal opened for voucher ${voucher.id}`, voucher);
      setVoucherEdits({});
      setErrors({});
    }
  }, [isOpen, voucher]);

  // Add a listener for the dialog close event
  useEffect(() => {
    const handleDialogClose = () => {
      console.log('Dialog close event detected');
      setIsOpen(false);
    };

    document.addEventListener('dialog-close', handleDialogClose);
    return () => {
      document.removeEventListener('dialog-close', handleDialogClose);
    };
  }, [setIsOpen]);

  const handleChange = (field: keyof Voucher, value: any) => {
    const newEdits = { ...voucherEdits, [field]: value };
    console.log(`Changing field ${field} to`, value);
    setVoucherEdits(newEdits);
    
    // Clear any error for this field
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const validateInputs = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (voucherEdits.preAuditedAmount !== undefined) {
      const amount = parseFloat(voucherEdits.preAuditedAmount.toString());
      if (isNaN(amount) || amount <= 0) {
        newErrors.preAuditedAmount = 'Pre-audited amount must be a positive number';
      }
    }
    
    if (voucherEdits.status === 'AUDIT: PROCESSING' && !voucherEdits.preAuditedBy) {
      newErrors.preAuditedBy = 'Pre-audited by is required';
    }

    if (voucherEdits.status === 'AUDIT: PROCESSING' && !voucherEdits.certifiedBy) {
      newErrors.certifiedBy = 'Certified by is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateInputs()) {
      toast.error("Please fix the errors before saving");
      return;
    }

    // Ensure status is set to move to PENDING DISPATCH
    const finalEdits: Partial<Voucher> = {
      ...voucherEdits,
      status: "AUDIT: PROCESSING", // Ensure status is set to move to PENDING DISPATCH
      lastUpdated: new Date().toISOString()
    };

    console.log('Saving voucher edits:', finalEdits);
    onSave(voucher.id, finalEdits);
  };

  if (!voucher) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-3xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Edit Voucher #{voucher.voucherNumber}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-2">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendorName">Vendor Name</Label>
              <Input
                id="vendorName"
                value={voucherEdits.vendorName !== undefined ? voucherEdits.vendorName : voucher.vendorName}
                onChange={(e) => handleChange('vendorName', e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="voucherNumber">Voucher Number</Label>
              <Input
                id="voucherNumber"
                value={voucher.voucherNumber}
                disabled
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Original Amount</Label>
              <Input
                id="amount"
                value={formatNumberWithCommas(voucher.amount)}
                disabled
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="preAuditedAmount" className={errors.preAuditedAmount ? 'text-red-500' : ''}>
                Pre-Audited Amount
              </Label>
              <Input
                id="preAuditedAmount"
                value={voucherEdits.preAuditedAmount !== undefined 
                  ? voucherEdits.preAuditedAmount 
                  : voucher.preAuditedAmount || voucher.amount}
                onChange={(e) => {
                  const value = e.target.value.replace(/,/g, '');
                  const numValue = value === '' ? 0 : parseFloat(value);
                  handleChange('preAuditedAmount', numValue);
                }}
                className={`mt-1 ${errors.preAuditedAmount ? 'border-red-500' : ''}`}
              />
              {errors.preAuditedAmount && (
                <p className="text-red-500 text-sm mt-1">{errors.preAuditedAmount}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="preAuditedBy" className={errors.preAuditedBy ? 'text-red-500' : ''}>
                Pre-Audited By
              </Label>
              <Select 
                value={voucherEdits.preAuditedBy !== undefined ? voucherEdits.preAuditedBy : voucher.preAuditedBy || ''}
                onValueChange={(value) => handleChange('preAuditedBy', value)}
              >
                <SelectTrigger className={`mt-1 ${errors.preAuditedBy ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select Auditor" />
                </SelectTrigger>
                <SelectContent>
                  {auditUsers.map((user) => (
                    <SelectItem key={user} value={user}>
                      {user}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.preAuditedBy && (
                <p className="text-red-500 text-sm mt-1">{errors.preAuditedBy}</p>
              )}
            </div>
            <div>
              <Label htmlFor="certifiedBy" className={errors.certifiedBy ? 'text-red-500' : ''}>
                Certified By
              </Label>
              <Select 
                value={voucherEdits.certifiedBy !== undefined ? voucherEdits.certifiedBy : voucher.certifiedBy || ''}
                onValueChange={(value) => handleChange('certifiedBy', value)}
              >
                <SelectTrigger className={`mt-1 ${errors.certifiedBy ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select Certifier" />
                </SelectTrigger>
                <SelectContent>
                  {auditUsers.map((user) => (
                    <SelectItem key={user} value={user}>
                      {user}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.certifiedBy && (
                <p className="text-red-500 text-sm mt-1">{errors.certifiedBy}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="comment">Comments</Label>
            <Textarea
              id="comment"
              value={voucherEdits.comment !== undefined ? voucherEdits.comment : voucher.comment || ''}
              onChange={(e) => handleChange('comment', e.target.value)}
              className="mt-1"
              rows={3}
            />
          </div>

          <div>
            <h3 className="text-lg font-medium">Voucher Details</h3>
            <div className="mt-2 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Invoice Date</Label>
                  <div className="mt-1 p-2 border rounded-md bg-gray-50">
                    {voucher.invoiceDate ? new Date(voucher.invoiceDate).toLocaleDateString() : 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Invoice Number</Label>
                  <div className="mt-1 p-2 border rounded-md bg-gray-50">
                    {voucher.invoiceNumber || 'N/A'}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Department</Label>
                  <div className="mt-1 p-2 border rounded-md bg-gray-50">
                    {voucher.department || 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Account Code</Label>
                  <div className="mt-1 p-2 border rounded-md bg-gray-50">
                    {voucher.accountCode || 'N/A'}
                  </div>
                </div>
              </div>
              <div>
                <Label>Description</Label>
                <div className="mt-1 p-2 border rounded-md bg-gray-50 min-h-[60px] whitespace-pre-wrap">
                  {voucher.description || 'No description provided'}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => setIsOpen(false)}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
