
import React from 'react';
import { AlertTriangle, Check, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Voucher } from '@/lib/types';

interface BatchInformationProps {
  batchesArray: Array<{
    dispatchTime: string;
    dispatchedBy: string;
    vouchers: Voucher[];
  }>;
  processedVouchers: string[];
  rejectedVouchers: string[];
  receivingVouchers: Voucher[];
  selectedBatchIndex?: number | null;
  onBatchClick?: (index: number) => void;
}

export function BatchInformation({ 
  batchesArray, 
  processedVouchers, 
  rejectedVouchers,
  receivingVouchers,
  selectedBatchIndex,
  onBatchClick
}: BatchInformationProps) {
  return (
    <>
      <div className="flex justify-between items-center py-1 px-1">
        <div className="text-xs text-muted-foreground">
          {batchesArray.length > 1 
            ? `${batchesArray.length} batches from Audit` 
            : `From: ${batchesArray[0]?.dispatchedBy || 'Audit'}`
          }
        </div>
        <div className="flex gap-2">
          <Badge variant="success" className="flex gap-1 items-center text-xs py-0.5">
            <Check className="h-3 w-3" /> {processedVouchers.length}
          </Badge>
          <Badge variant="destructive" className="flex gap-1 items-center text-xs py-0.5">
            <X className="h-3 w-3" /> {rejectedVouchers.length}
          </Badge>
          <Badge variant="outline" className="flex gap-1 items-center text-xs py-0.5">
            <AlertTriangle className="h-3 w-3" /> {receivingVouchers.length - processedVouchers.length - rejectedVouchers.length}
          </Badge>
        </div>
      </div>
      
      {batchesArray.length > 1 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-1 mb-2">
          {batchesArray.map((batch, index) => (
            <div 
              key={index} 
              className={`border rounded-md p-1 bg-muted/30 cursor-pointer hover:bg-muted/50 transition-colors ${selectedBatchIndex === index ? 'border-blue-500 bg-blue-900/10' : ''}`}
              onClick={() => onBatchClick && onBatchClick(index)}
            >
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-xs font-medium">Batch #{index + 1}</p>
                  <p className="text-xs text-muted-foreground truncate">By: {batch.dispatchedBy}</p>
                </div>
                <Badge className="text-xs">{batch.vouchers.length}</Badge>
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
}
