
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { X } from 'lucide-react';
import { toast } from 'sonner';

interface RejectionCommentDialogProps {
  isOpen: boolean;
  voucherId: string;
  onClose: () => void;
  onConfirm: (voucherId: string, comment: string) => void;
  isReturnedVoucher?: boolean;
}

export function RejectionCommentDialog({
  isOpen,
  voucherId,
  onClose,
  onConfirm,
  isReturnedVoucher = false
}: RejectionCommentDialogProps) {
  const [comment, setComment] = useState('');

  const handleConfirm = () => {
    if (!comment.trim()) {
      toast.error("A rejection reason is required", {
        duration: 3000,
      });
      return;
    }

    // Always pass comment as a string, never an object
    const commentText = String(comment.trim());
    console.log(`Confirming rejection with comment: "${commentText}"`);
    onConfirm(voucherId, commentText);
    setComment('');
  };

  const handleClose = () => {
    setComment('');
    onClose();
  };

  // Adjust dialog title and button text based on whether it's a returned voucher
  const dialogTitle = isReturnedVoucher
    ? "Provide Rejection Reason for Returned Voucher"
    : "Provide Rejection Reason";

  const confirmButtonText = isReturnedVoucher
    ? "Confirm Rejection of Return"
    : "Confirm Rejection";

  return (
    <Dialog open={isOpen} onOpenChange={() => handleClose()}>
      <DialogContent className="bg-black text-white border-white/10 max-w-md">
        <div className="absolute right-4 top-4 cursor-pointer" onClick={handleClose}>
          <X className="h-4 w-4 text-white/70" />
        </div>

        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-white uppercase">
            {dialogTitle}
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="text-sm font-medium text-white mb-2 uppercase">
            Rejection Reason (Required)
          </div>
          <Textarea
            value={comment}
            onChange={(e) => setComment(e.target.value.toUpperCase())}
            placeholder="ENTER REJECTION REASON"
            className="resize-none h-32 bg-gray-900 text-white border-gray-700 focus:border-blue-500 placeholder:text-gray-500"
            autoFocus
          />
        </div>

        <DialogFooter className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={handleClose}
            className="bg-transparent border-gray-700 text-white hover:bg-gray-800 uppercase"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-red-600 hover:bg-red-700 text-white uppercase"
          >
            {confirmButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
