
import { Voucher } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/utils/formatUtils';
import { Badge } from '@/components/ui/badge';
import { ArrowDownToLine, RefreshCw } from 'lucide-react';

interface VoucherTabsContentProps {
  voucher: Voucher | null;
  onAccept: () => void;
  onReject: () => void;
  isReturned?: boolean;
}

export function VoucherTabsContent({ 
  voucher, 
  onAccept, 
  onReject,
  isReturned = false
}: VoucherTabsContentProps) {
  if (!voucher) {
    return (
      <div className="py-8 text-center text-gray-500">
        No voucher selected or available for processing.
      </div>
    );
  }
  
  // Get comment safely
  const getCommentDisplay = (): string => {
    if (isReturned && voucher.returnComment) {
      return String(voucher.returnComment);
    }
    
    if (voucher.comment) {
      return String(voucher.comment);
    }
    
    return "";
  };

  return (
    <div className="space-y-6 pt-4 pb-6">
      <div className="grid grid-cols-4 gap-4">
        <div className="col-span-4 md:col-span-2">
          <div className="flex justify-between">
            <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Voucher ID</div>
            <div>
              {isReturned ? (
                <Badge variant="destructive" className="ml-2 uppercase">
                  Returning
                </Badge>
              ) : null}
              {voucher.status === 'VOUCHER REJECTED' ? (
                <Badge variant="destructive" className="ml-2 uppercase">
                  Rejected
                </Badge>
              ) : null}
              {voucher.status === 'VOUCHER CERTIFIED' && !isReturned ? (
                <Badge variant="outline" className="ml-2 uppercase">
                  Certified
                </Badge>
              ) : null}
            </div>
          </div>
          <div className="font-mono text-lg">{voucher.voucherId}</div>
        </div>
        <div className="col-span-4 md:col-span-2">
          <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Date</div>
          <div className="font-mono">{voucher.date}</div>
        </div>
        <div className="col-span-4 md:col-span-2">
          <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Claimant</div>
          <div className="font-mono uppercase">{voucher.claimant}</div>
        </div>
        <div className="col-span-4 md:col-span-2">
          <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Amount</div>
          <div className="font-mono">
            {formatCurrency(voucher.preAuditedAmount || voucher.amount, voucher.currency)}
          </div>
        </div>
        <div className="col-span-4">
          <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Description</div>
          <div className="font-mono uppercase">{voucher.description}</div>
        </div>
        {voucher.certifiedBy && (
          <div className="col-span-4 md:col-span-2">
            <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Certified By</div>
            <div className="font-mono uppercase">{voucher.certifiedBy}</div>
          </div>
        )}
        {voucher.preAuditedBy && (
          <div className="col-span-4 md:col-span-2">
            <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Pre-Audited By</div>
            <div className="font-mono uppercase">{voucher.preAuditedBy}</div>
          </div>
        )}
        {voucher.auditDispatchedBy && (
          <div className="col-span-4 md:col-span-2">
            <div className="mb-1 text-xs font-medium text-gray-400 uppercase">Dispatched By</div>
            <div className="font-mono uppercase">{voucher.auditDispatchedBy}</div>
          </div>
        )}
        {getCommentDisplay() && (
          <div className="col-span-4">
            <div className="mb-1 text-xs font-medium text-gray-400 uppercase">
              {isReturned ? "Return Reason" : "Comment"}
            </div>
            <div className="font-mono uppercase">{getCommentDisplay()}</div>
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2 pt-6">
        <Button 
          variant="outline"
          onClick={onReject} 
          className="bg-transparent border-gray-700 hover:bg-gray-800"
        >
          REJECT
        </Button>
        <Button 
          onClick={onAccept}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isReturned ? (
            <RefreshCw className="mr-2 h-4 w-4" />
          ) : (
            <ArrowDownToLine className="mr-2 h-4 w-4" />
          )}
          ACCEPT {isReturned ? "RETURNED VOUCHER" : ""}
        </Button>
      </div>
    </div>
  );
}
