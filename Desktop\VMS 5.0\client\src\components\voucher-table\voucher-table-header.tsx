import { ArrowUpDown } from 'lucide-react';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Voucher } from '@/lib/types';

interface SortableColumnHeaderProps {
  column: string;
  label: string;
  sortColumn: string | null;
  handleSort: (column: string) => void;
}

function SortableColumnHeader({ column, label, sortColumn, handleSort, className }: SortableColumnHeaderProps) {
  return (
    <th className={`p-4 text-center font-medium ${className || ''}`}>
      <button
        className="flex items-center justify-center font-medium w-full uppercase"
        onClick={() => handleSort(column)}
      >
        <span>{label}</span>
        {sortColumn === column && (
          <ArrowUpDown className="h-4 w-4 ml-1" />
        )}
      </button>
    </th>
  );
}

interface VoucherTableHeaderProps {
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  selectable: boolean;
  handleSelectAll: () => void;
  selectedVouchers: string[];
  filteredVouchers: Voucher[];
  view: string;
  isAudit: boolean;
  showPreAuditedBy: boolean;
}

export function VoucherTableHeader({
  sortColumn,
  handleSort,
  selectable,
  handleSelectAll,
  selectedVouchers,
  filteredVouchers,
  view,
  isAudit,
  showPreAuditedBy
}: VoucherTableHeaderProps) {
  // Only consider selectable vouchers for the "select all" checkbox state
  const selectableVouchers = filteredVouchers.filter(
    v => v.status === "PENDING SUBMISSION" && !v.sentToAudit
  );

  return (
    <div className="w-full bg-background">
      <table className="w-full table-fixed" style={{ tableLayout: 'fixed' }}>
        <thead>
          <tr className="bg-background">
          {selectable && (
            <th className="w-[5%] sticky left-0 bg-background z-20 p-4 text-center">
              <Checkbox
                checked={selectedVouchers.length === selectableVouchers.length && selectableVouchers.length > 0}
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
                disabled={selectableVouchers.length === 0}
              />
            </th>
          )}
          <th className="sticky left-0 bg-background z-20 uppercase w-[15%] p-4 text-center font-medium">VOUCHER ID</th>
          <SortableColumnHeader column="date" label="DATE" sortColumn={sortColumn} handleSort={handleSort} className="w-[20%]" />
          <SortableColumnHeader column="claimant" label="CLAIMANT" sortColumn={sortColumn} handleSort={handleSort} className="w-[20%]" />
          <SortableColumnHeader column="description" label="DESCRIPTION" sortColumn={sortColumn} handleSort={handleSort} className="w-[25%]" />
          <SortableColumnHeader column="amount" label="AMOUNT" sortColumn={sortColumn} handleSort={handleSort} className="w-[10%]" />

          {/* Show CERTIFIED AMT column only for certified tab */}
          {!isAudit && view === 'certified' && (
            <SortableColumnHeader column="preAuditedAmount" label="CERTIFIED AMT" sortColumn={sortColumn} handleSort={handleSort} className="w-[15%]" />
          )}

          {isAudit && showPreAuditedBy && view !== 'rejected' && (
            <>
              <SortableColumnHeader column="preAuditedAmount" label="CERTIFIED AMT" sortColumn={sortColumn} handleSort={handleSort} className="w-[15%]" />
              <th className="uppercase w-[15%] p-4 text-center font-medium">CERTIFIED BY</th>
            </>
          )}

          {view === 'dispatched' && (
            <>
              <th className="uppercase w-[15%] p-4 text-center font-medium">CERTIFIED BY</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">TAX</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">DISPATCHED BY</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">DISPATCHED ON</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">RECEIVED BY</th>
            </>
          )}

          {view === 'returned' && (
            <>
              <th className="uppercase w-[15%] p-4 text-center font-medium">DATE RETURNED</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">STATUS</th>
              <th className="uppercase w-[25%] p-4 text-center font-medium">COMMENT</th>
            </>
          )}

          {view === 'rejected' && (
            <th className="uppercase w-[15%] p-4 text-center font-medium">REJECTION TIME</th>
          )}

          {isAudit && view === 'rejected' && (
            <>
              <th className="uppercase w-[15%] p-4 text-center font-medium">CREATED BY</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">DISPATCHED BY</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">REJECTED BY</th>
            </>
          )}

          {view !== 'rejected' && view !== 'returned' && (
            <th className="uppercase w-[15%] p-4 text-center font-medium">STATUS</th>
          )}

          <th className="text-center uppercase sticky right-0 bg-background z-20 w-[10%] p-4 font-medium">ACTIONS</th>
          </tr>
        </thead>
      </table>
    </div>
  );
}
