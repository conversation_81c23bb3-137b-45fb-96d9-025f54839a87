import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Voucher, Department } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';

// Import sub-components
import { VoucherTableHeader } from './voucher-table-header';
import { VoucherTableBody } from './voucher-table-body';
import { useVoucherTable } from './use-voucher-table';

interface VoucherTableProps {
  vouchers: Voucher[];
  department: Department;
  selectable?: boolean;
  onSelectionChange?: (selectedIds: string[]) => void;
  isAudit?: boolean;
  view?: 'pending-submission' | 'processing' | 'certified' | 'rejected' | 'returned' | 'dispatched' | 'all';
  onViewVoucher?: (voucher: Voucher) => void;
  showAddBack?: boolean;
  showDelete?: boolean;
  showPreAuditedBy?: boolean;
  showScrollBars?: boolean;
}

export function VoucherTable({
  vouchers,
  department,
  selectable = false,
  onSelectionChange,
  isAudit = false,
  view = 'pending-submission',
  onViewVoucher,
  showAddBack = false,
  showDelete = false,
  showPreAuditedBy = false,
  showScrollBars = false
}: VoucherTableProps) {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const tableRef = useRef<HTMLDivElement>(null);

  const {
    sortColumn,
    sortDirection,
    selectedVouchers,
    setSelectedVouchers,
    handleSort,
    handleSelectVoucher,
    handleSelectAll,
    filteredVouchers,
    handleViewVoucher,
    handleAddBack,
    handleDeleteVoucher
  } = useVoucherTable({
    vouchers,
    searchTerm,
    department,
    selectable,
    onSelectionChange,
    onViewVoucher,
    navigate
  });

  return (
    <div className="flex flex-col h-full">
      {/* Fixed search bar and column headings as one unit - outside the scrollable area */}
      <div className="sticky top-0 z-50 bg-background shadow-sm border-b rounded-t-md border border-b-0">
        {/* Search bar */}
        <div className="p-2 border-b">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="SEARCH VOUCHERS..."
              className="pl-8 w-full uppercase"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value.toUpperCase())}
            />
          </div>
        </div>

        {/* Column headings */}
        <div className="min-w-[1200px] overflow-hidden" style={{ tableLayout: 'fixed' }}>
          <VoucherTableHeader
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            handleSort={handleSort}
            selectable={selectable}
            handleSelectAll={handleSelectAll}
            selectedVouchers={selectedVouchers}
            filteredVouchers={filteredVouchers}
            view={view}
            isAudit={isAudit}
            showPreAuditedBy={showPreAuditedBy}
          />
        </div>
      </div>

      {/* Scrollable table body */}
      <div className="rounded-b-md border border-t-0 flex-1 flex flex-col min-h-0">
        <div className="overflow-auto h-full" ref={tableRef}>
          <div className="min-w-[1200px]" style={{ tableLayout: 'fixed' }}>
            <VoucherTableBody
              filteredVouchers={filteredVouchers}
              selectable={selectable}
              selectedVouchers={selectedVouchers}
              handleSelectVoucher={handleSelectVoucher}
              handleViewVoucher={handleViewVoucher}
              handleAddBack={handleAddBack}
              handleDeleteVoucher={handleDeleteVoucher}
              view={view}
              isAudit={isAudit}
              showAddBack={showAddBack}
              showDelete={showDelete}
              showPreAuditedBy={showPreAuditedBy}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
