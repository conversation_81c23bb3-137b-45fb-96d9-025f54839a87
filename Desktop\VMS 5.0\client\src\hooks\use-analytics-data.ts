import { useState, useEffect } from 'react';
import { useAppStore } from '@/lib/store';

export function useAnalyticsData(timeRange: 'month' | 'quarter' | 'year') {
  const [isLoading, setIsLoading] = useState(true);
  const vouchers = useAppStore((state) => state.vouchers);
  const provisionalCashRecords = useAppStore((state) => state.provisionalCashRecords);
  const users = useAppStore((state) => state.users);

  const [summaryData, setSummaryData] = useState({
    totalVouchers: 0,
    vouchersTrend: 'up' as const,
    vouchersTrendValue: '+5% from last period',

    avgProcessingTime: 0,
    processingTimeTrend: 'down' as const,
    processingTimeTrendValue: '-10% from last period',

    certificationRate: 0,
    certificationRateTrend: 'up' as const,
    certificationRateTrendValue: '+2% from last period',

    totalValue: 0,
    totalValueTrend: 'up' as const,
    totalValueTrendValue: '+15% from last period',

    pendingVouchers: 0,
    pendingVouchersTrend: 'down' as const,
    pendingVouchersTrendValue: '-8% from last period',
  });

  const [savingsData, setSavingsData] = useState({
    departmental: [] as any[],
    totalSavings: 0,
    savingsPercentage: 0,
    defaultCurrency: 'GHS',
    currencySummary: {} as Record<string, { totalSavings: number; totalOriginalAmount: number; totalCertifiedAmount: number }>
  });

  const [userActivityData, setUserActivityData] = useState({
    userMetrics: [] as any[],
    activityHeatmap: [] as any[],
    topPerformer: '',
    bottomPerformer: '',
    monthlyTopPerformers: [] as any[],
    yearlyPreAuditTopPerformer: '',
    yearlyPreAuditSavings: 0,
    yearlyCertificationTopPerformer: '',
    yearlyCertificationRate: 0
  });

  const [provisionalCashData, setProvisionalCashData] = useState({
    departmentRecords: [] as any[],
    agingData: [] as any[],
    totalOutstanding: 0,
    totalCleared: 0,
    overallClearanceRate: 0
  });

  useEffect(() => {
    setIsLoading(true);

    // In a real application, this would fetch data from an API
    // For this demo, we'll generate mock data based on the vouchers in the store

    setTimeout(() => {
      // Calculate summary data
      const certifiedVouchers = vouchers.filter(v => v.status === "VOUCHER CERTIFIED");
      const rejectedVouchers = vouchers.filter(v => v.status === "VOUCHER REJECTED");
      const pendingVouchers = vouchers.filter(v =>
        v.status === "PENDING SUBMISSION" ||
        v.status === "PENDING RECEIPT" ||
        v.status === "AUDIT: PROCESSING"
      );

      const totalProcessed = certifiedVouchers.length + rejectedVouchers.length;
      const certificationRate = totalProcessed > 0
        ? Math.round((certifiedVouchers.length / totalProcessed) * 100)
        : 0;

      const totalValue = certifiedVouchers.reduce((sum, v) => sum + v.amount, 0);

      // Calculate pre-audit savings
      const savings = certifiedVouchers.reduce((sum, v) => {
        const originalAmount = v.amount;
        const certifiedAmount = v.preAuditedAmount || v.amount;
        return sum + (originalAmount - certifiedAmount);
      }, 0);

      const totalOriginalAmount = certifiedVouchers.reduce((sum, v) => sum + v.amount, 0);
      const savingsPercentage = totalOriginalAmount > 0
        ? (savings / totalOriginalAmount) * 100
        : 0;

      // Generate mock data for departmental savings with monthly breakdown
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const departments = ['FINANCE', 'MINISTRIES', 'PENSIONS', 'PENTMEDIA', 'MISSIONS', 'PENTSOS'];

      // Initialize currency tracking
      const currencies = ['GHS', 'USD', 'EUR', 'GBP'];
      const defaultCurrency = 'GHS';
      const currencySummary: Record<string, { totalSavings: number; totalOriginalAmount: number; totalCertifiedAmount: number }> = {};

      currencies.forEach(currency => {
        currencySummary[currency] = {
          totalSavings: 0,
          totalOriginalAmount: 0,
          totalCertifiedAmount: 0
        };
      });

      const departmentalSavings = departments.map(dept => {
        // Assign a primary currency to each department (mostly GHS, but some have other currencies)
        const deptCurrency = Math.random() > 0.7 ? currencies[Math.floor(Math.random() * currencies.length)] : defaultCurrency;

        // Generate monthly savings for this department
        const monthlySavings = months.map(month => {
          // Some months might have different currencies
          const monthCurrency = Math.random() > 0.8 ? currencies[Math.floor(Math.random() * currencies.length)] : deptCurrency;

          const originalAmount = Math.random() * 500000 + 100000;
          const certifiedAmount = originalAmount * (0.85 + Math.random() * 0.1);
          const savings = originalAmount - certifiedAmount;
          const percentage = (savings / originalAmount) * 100;

          return {
            month,
            savings,
            percentage,
            originalAmount,
            certifiedAmount,
            currency: monthCurrency
          };
        });

        // Create a currency summary for this department
        const deptCurrencySummary: Record<string, { savings: number; originalAmount: number; certifiedAmount: number }> = {};
        monthlySavings.forEach(month => {
          if (!deptCurrencySummary[month.currency]) {
            deptCurrencySummary[month.currency] = {
              savings: 0,
              originalAmount: 0,
              certifiedAmount: 0
            };
          }
          deptCurrencySummary[month.currency].savings += month.savings;
          deptCurrencySummary[month.currency].originalAmount += month.originalAmount;
          deptCurrencySummary[month.currency].certifiedAmount += month.certifiedAmount;
        });

        // Calculate yearly totals (using the department's primary currency)
        const yearlyOriginalAmount = monthlySavings
          .filter(m => m.currency === deptCurrency)
          .reduce((sum, m) => sum + m.originalAmount, 0);
        const yearlyCertifiedAmount = monthlySavings
          .filter(m => m.currency === deptCurrency)
          .reduce((sum, m) => sum + m.certifiedAmount, 0);
        const yearlySavings = yearlyOriginalAmount - yearlyCertifiedAmount;
        const yearlyPercentage = yearlyOriginalAmount > 0 ? (yearlySavings / yearlyOriginalAmount) * 100 : 0;

        // Update the global currency summary
        if (!currencySummary[deptCurrency]) {
          currencySummary[deptCurrency] = {
            totalSavings: 0,
            totalOriginalAmount: 0,
            totalCertifiedAmount: 0
          };
        }
        currencySummary[deptCurrency].totalSavings += yearlySavings;
        currencySummary[deptCurrency].totalOriginalAmount += yearlyOriginalAmount;
        currencySummary[deptCurrency].totalCertifiedAmount += yearlyCertifiedAmount;

        return {
          name: dept,
          savings: yearlySavings,
          percentage: yearlyPercentage,
          originalAmount: yearlyOriginalAmount,
          certifiedAmount: yearlyCertifiedAmount,
          currency: deptCurrency,
          monthlySavings,
          yearlySavings: {
            savings: yearlySavings,
            percentage: yearlyPercentage,
            originalAmount: yearlyOriginalAmount,
            certifiedAmount: yearlyCertifiedAmount,
            currency: deptCurrency
          },
          currencySummary: deptCurrencySummary
        };
      });

      // Generate mock data for user activity metrics
      const auditUsers = users.filter(u => u.department === 'AUDIT').map(u => u.name);
      const userMetrics = auditUsers.map(name => {
        const vouchersProcessed = Math.floor(Math.random() * 100) + 20;
        const preAuditSavings = Math.floor(Math.random() * 500000) + 50000;
        const originalAmount = preAuditSavings * (100 / (Math.floor(Math.random() * 15) + 5));
        const preAuditSavingsPercentage = (preAuditSavings / originalAmount) * 100;

        return {
          name,
          vouchersProcessed,
          avgProcessingTime: Math.floor(Math.random() * 24) + 6,
          certificationRate: Math.floor(Math.random() * 30) + 70,
          rejectionRate: Math.floor(Math.random() * 30),
          preAuditSavings,
          preAuditSavingsPercentage
        };
      });

      // Sort users by vouchers processed to find top and bottom performers
      const sortedUsers = [...userMetrics].sort((a, b) => b.vouchersProcessed - a.vouchersProcessed);
      const topPerformer = sortedUsers[0]?.name || 'N/A';
      const bottomPerformer = sortedUsers[sortedUsers.length - 1]?.name || 'N/A';

      // Find yearly top performers
      const sortedByPreAudit = [...userMetrics].sort((a, b) => b.preAuditSavings - a.preAuditSavings);
      const yearlyPreAuditTopPerformer = sortedByPreAudit[0]?.name || 'N/A';
      const yearlyPreAuditSavings = sortedByPreAudit[0]?.preAuditSavings || 0;

      const sortedByCertification = [...userMetrics].sort((a, b) => b.certificationRate - a.certificationRate);
      const yearlyCertificationTopPerformer = sortedByCertification[0]?.name || 'N/A';
      const yearlyCertificationRate = sortedByCertification[0]?.certificationRate || 0;

      // Generate monthly top performers
      const monthlyTopPerformers = months.map(month => {
        // Randomly select users for each month
        const randomUserIndex1 = Math.floor(Math.random() * userMetrics.length);
        const randomUserIndex2 = Math.floor(Math.random() * userMetrics.length);

        return {
          month,
          preAuditTopPerformer: userMetrics[randomUserIndex1].name,
          preAuditSavings: Math.floor(Math.random() * 200000) + 50000,
          certificationTopPerformer: userMetrics[randomUserIndex2].name,
          certificationRate: Math.floor(Math.random() * 20) + 80
        };
      });

      // Generate activity heatmap data
      const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
      const activityHeatmap = days.map(day => ({
        day,
        hour: Math.floor(Math.random() * 8) + 9, // 9 AM to 5 PM
        value: Math.floor(Math.random() * 30) + 5
      }));

      // Generate provisional cash analysis data
      const departmentRecords = departments.map(department => {
        const total = Math.floor(Math.random() * 500000) + 100000;
        const clearanceRate = Math.floor(Math.random() * 60) + 40;
        const cleared = Math.floor(total * (clearanceRate / 100));
        const outstanding = total - cleared;
        const totalCertifiedAmount = Math.floor(Math.random() * 2000000) + 500000;

        // Generate mock transactions for each department
        const transactionCount = Math.floor(Math.random() * 10) + 5;
        const transactions = Array.from({ length: transactionCount }).map((_, index) => {
          const isCleared = Math.random() > (outstanding / total);
          const amount = Math.floor(Math.random() * 50000) + 10000;
          const date = new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000)
            .toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });

          // Randomly select a currency
          const currencies = ['GHS', 'USD', 'EUR', 'GBP'];
          const currencyIndex = Math.floor(Math.random() * currencies.length);
          const currency = currencies[currencyIndex];

          return {
            id: `${department}-${index}`,
            voucherId: `VCH-${Math.floor(Math.random() * 10000)}`,
            claimant: `Claimant ${index + 1}`,
            amount,
            currency,
            date,
            status: isCleared ? 'cleared' : 'not-cleared'
          };
        });

        return {
          department,
          outstanding,
          cleared,
          total,
          clearanceRate,
          totalCertifiedAmount,
          transactions
        };
      });

      // Generate aging data for provisional cash
      const agingRanges = ['0-30 days', '31-60 days', '61-90 days', '91+ days'];
      const agingData = agingRanges.map(range => ({
        range,
        value: Math.floor(Math.random() * 300000) + 50000,
        count: Math.floor(Math.random() * 20) + 5
      }));

      // Calculate totals for provisional cash
      const totalOutstanding = departmentRecords.reduce((sum, record) => sum + record.outstanding, 0);
      const totalCleared = departmentRecords.reduce((sum, record) => sum + record.cleared, 0);
      const overallClearanceRate = Math.round((totalCleared / (totalOutstanding + totalCleared)) * 100);

      // Update state with calculated data
      setSummaryData({
        totalVouchers: totalProcessed,
        vouchersTrend: 'up',
        vouchersTrendValue: '+5% from last period',

        avgProcessingTime: 18,
        processingTimeTrend: 'down',
        processingTimeTrendValue: '-10% from last period',

        certificationRate,
        certificationRateTrend: 'up',
        certificationRateTrendValue: '+2% from last period',

        totalValue,
        totalValueTrend: 'up',
        totalValueTrendValue: '+15% from last period',

        pendingVouchers: pendingVouchers.length,
        pendingVouchersTrend: 'down',
        pendingVouchersTrendValue: '-8% from last period',
      });

      setSavingsData({
        departmental: departmentalSavings,
        totalSavings: savings,
        savingsPercentage: savingsPercentage,
        defaultCurrency: defaultCurrency,
        currencySummary: currencySummary
      });

      setUserActivityData({
        userMetrics,
        activityHeatmap,
        topPerformer,
        bottomPerformer,
        monthlyTopPerformers,
        yearlyPreAuditTopPerformer,
        yearlyPreAuditSavings,
        yearlyCertificationTopPerformer,
        yearlyCertificationRate
      });

      setProvisionalCashData({
        departmentRecords,
        agingData,
        totalOutstanding,
        totalCleared,
        overallClearanceRate
      });

      setIsLoading(false);
    }, 1000);
  }, [vouchers, users, provisionalCashRecords, timeRange]);

  return {
    summaryData,
    savingsData,
    userActivityData,
    provisionalCashData,
    isLoading
  };
}
