
import { useAppStore } from '@/lib/store';
import { Department, Voucher } from '@/lib/types';
import { useEffect, useState } from 'react';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export function useDepartmentData(department?: Department, refreshTrigger: number = 0) {
  const [data, setData] = useState({
    vouchers: [] as Voucher[],
    pendingVouchers: [] as Voucher[],
    pendingSubmissionVouchers: [] as Voucher[],
    processingVouchers: [] as Voucher[],
    certifiedVouchers: [] as Voucher[],
    rejectedVouchers: [] as Voucher[],
    returnedVouchers: [] as Voucher[],
    vouchersToReceive: [] as Voucher[],
    departmentBatches: [] as any[],
    batchesArray: [] as any[]
  });

  const getVouchersForDepartment = useAppStore((state) => state.getVouchersForDepartment);
  const getPendingVouchersForDepartment = useAppStore((state) => state.getPendingVouchersForDepartment);
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const vouchers = useAppStore(state => state.vouchers);

  useEffect(() => {
    if (!department) {
      return;
    }

    const departmentVouchers = getVouchersForDepartment(department);
    const pendingVouchers = getPendingVouchersForDepartment(department);

    // Debug output to see all department vouchers, especially rejected and returned ones
    console.log(`All ${department} vouchers (${departmentVouchers.length}):`);
    departmentVouchers.filter(v => v.status === VOUCHER_STATUSES.VOUCHER_REJECTED || v.status === VOUCHER_STATUSES.VOUCHER_RETURNED || v.isReturned || v.pendingReturn).forEach(v => {
      console.log(`Found special ${department} voucher: ${v.voucherId} (ID: ${v.id}), status: ${v.status}, deleted: ${v.deleted}, isReturned: ${v.isReturned}, pendingReturn: ${v.pendingReturn}`);
    });

    // CRITICAL FIX: Properly filter pending submission vouchers
    // They should have PENDING SUBMISSION status and not be sent to audit yet
    const pendingSubmissionVouchers = departmentVouchers.filter(v => {
      const isPendingSubmission = v.status === VOUCHER_STATUSES.PENDING_SUBMISSION;
      const notSentToAudit = !v.sentToAudit;
      const notDeleted = !v.deleted;

      // Debug log to help diagnose issues
      if (isPendingSubmission) {
        console.log(`Evaluating voucher for PENDING tab: ${v.voucherId}, status=${v.status}, sentToAudit=${v.sentToAudit}, deleted=${v.deleted}, include=${isPendingSubmission && notSentToAudit && notDeleted}`);
      }

      return isPendingSubmission && notSentToAudit && notDeleted;
    });

    // Log the count of pending submission vouchers
    console.log(`Found ${pendingSubmissionVouchers.length} pending submission vouchers for ${department}`);

    console.log(`Final pending submission vouchers count for ${department}: ${pendingSubmissionVouchers.length}`);
    if (pendingSubmissionVouchers.length > 0) {
      console.log('First few pending submission vouchers:', pendingSubmissionVouchers.slice(0, 3).map(v => `${v.voucherId} (${v.id})`));
    }

    // Debug log for all department vouchers to help identify issues
    console.log(`All ${department} vouchers (${departmentVouchers.length}) with sentToAudit=true:`);
    departmentVouchers.filter(v => v.sentToAudit).forEach(v => {
      console.log(`Voucher ${v.voucherId} (ID: ${v.id}): status=${v.status}, sentToAudit=${v.sentToAudit}, auditDispatchTime=${v.auditDispatchTime}, dispatchedBy=${v.dispatchedBy}, dispatchTime=${v.dispatchTime}`);
    });

    // Processing vouchers - vouchers that have been sent to audit but not yet returned
    const processingVouchers = departmentVouchers.filter(v => {
      // Debug log for vouchers that might be in processing
      if (v.sentToAudit && !v.auditDispatchTime) {
        console.log(`Evaluating voucher for PROCESSING tab: ${v.voucherId}, status=${v.status}, sentToAudit=${v.sentToAudit}`);
      }

      // FIXED: Strict adherence to the voucher movement algorithm
      // A voucher should be in PROCESSING if:
      // 1. It has been sent to audit (sentToAudit=true)
      // 2. It has status PENDING RECEIPT or AUDIT: PROCESSING
      // 3. It has not been dispatched back from audit yet
      // 4. It is not deleted
      return (
        // Must be sent to audit
        v.sentToAudit &&
        // Must have appropriate status
        (v.status === VOUCHER_STATUSES.PENDING_RECEIPT || v.status === VOUCHER_STATUSES.AUDIT_PROCESSING) &&
        // Must not be dispatched back from audit yet
        !v.auditDispatchTime &&
        // Must not be deleted
        !v.deleted &&
        // Must not be certified, rejected or returned yet
        v.status !== VOUCHER_STATUSES.VOUCHER_CERTIFIED &&
        v.status !== VOUCHER_STATUSES.VOUCHER_REJECTED &&
        v.status !== VOUCHER_STATUSES.VOUCHER_RETURNED
      );
    });

    // Log the count of processing vouchers
    console.log(`Found ${processingVouchers.length} processing vouchers for ${department}`);

    // Certified vouchers - have VOUCHER CERTIFIED status and not returned
    const certifiedVouchers = departmentVouchers.filter(v =>
      v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED &&
      !v.isReturned &&
      !v.pendingReturn
    );

    // Rejected vouchers - CRITICAL FIX: Properly filter for rejected vouchers
    const rejectedVouchers = departmentVouchers.filter(v => {
      const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
      const isNotDeleted = v.deleted !== true;

      if (isRejectedStatus) {
        console.log(`Evaluating rejected voucher ${v.voucherId} for ${department} tab: status=${v.status}, deleted=${v.deleted}, include=${isRejectedStatus && isNotDeleted}`);
      }

      return isRejectedStatus && isNotDeleted;
    });

    console.log(`Final rejected vouchers count for ${department}: ${rejectedVouchers.length}`);

    // Returned vouchers - either marked as isReturned or have VOUCHER RETURNED status
    const returnedVouchers = departmentVouchers.filter(v => {
      const isReturned = v.isReturned === true || v.status === VOUCHER_STATUSES.VOUCHER_RETURNED;
      const hasBeenReceived = v.departmentReceiptTime !== undefined;

      if (isReturned) {
        console.log(`Evaluating returned voucher ${v.voucherId} for ${department} tab: isReturned=${v.isReturned}, status=${v.status}, hasBeenReceived=${hasBeenReceived}`);
      }

      return isReturned && hasBeenReceived;
    });

    console.log(`Final returned vouchers count for ${department}: ${returnedVouchers.length}`);

    // Vouchers to receive - processed and dispatched FROM Audit TO department
    // Include both certified and rejected and returned vouchers
    const vouchersToReceive = departmentVouchers.filter(v => {
      const hasAuditDispatchInfo = v.auditDispatchedBy && v.auditDispatchTime;
      const notYetReceived = !v.departmentReceiptTime;
      const isEligible =
        v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED ||
        v.status === VOUCHER_STATUSES.VOUCHER_REJECTED ||
        v.status === VOUCHER_STATUSES.VOUCHER_RETURNED ||
        v.isReturned ||
        v.pendingReturn;

      return hasAuditDispatchInfo && notYetReceived && isEligible;
    });

    console.log(`Vouchers to receive for ${department}: ${vouchersToReceive.length}`,
      vouchersToReceive.map(v => `${v.voucherId} (${v.id}): status=${v.status}, isReturned=${v.isReturned}, pendingReturn=${v.pendingReturn}`)
    );

    // Get batches for the current department
    const departmentBatches = voucherBatches.filter(batch => {
      // For outgoing batches (department -> audit)
      if (batch.department === department && !batch.received && !batch.fromAudit) {
        return true;
      }

      // For incoming batches (audit -> department)
      if (batch.department === department && !batch.received && batch.fromAudit) {
        return true;
      }

      return false;
    });

    // Group vouchers to receive by dispatch time
    const voucherBatchesByDispatchTime = vouchersToReceive.reduce((batches, voucher) => {
      // Include all eligible vouchers (certified, rejected, and returned)
      if (!(
        voucher.certifiedBy ||
        voucher.status === VOUCHER_STATUSES.VOUCHER_REJECTED ||
        voucher.status === VOUCHER_STATUSES.VOUCHER_RETURNED ||
        voucher.isReturned ||
        voucher.pendingReturn
      )) {
        return batches;
      }

      const batchKey = voucher.auditDispatchTime || 'unknown';
      if (!batches[batchKey]) {
        batches[batchKey] = {
          dispatchTime: voucher.auditDispatchTime || 'unknown',
          dispatchedBy: voucher.auditDispatchedBy || 'Audit Department',
          vouchers: []
        };
      }
      batches[batchKey].vouchers.push(voucher);
      return batches;
    }, {} as Record<string, { dispatchTime: string; dispatchedBy: string; vouchers: Voucher[] }>);

    const batchesArray = Object.values(voucherBatchesByDispatchTime);

    setData({
      vouchers: departmentVouchers,
      pendingVouchers,
      pendingSubmissionVouchers,
      processingVouchers,
      certifiedVouchers,
      rejectedVouchers,
      returnedVouchers,
      vouchersToReceive,
      departmentBatches,
      batchesArray
    });

  }, [department, getVouchersForDepartment, getPendingVouchersForDepartment, voucherBatches, refreshTrigger, vouchers]);

  return data;
}
