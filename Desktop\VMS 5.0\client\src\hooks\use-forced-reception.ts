import { useState, useEffect, useCallback } from 'react';
import { useAppStore } from '@/lib/store';
import { Department } from '@/lib/types';

export function useForcedReception(department: Department, refreshTrigger: number = 0) {
  const [hasNewVouchers, setHasNewVouchers] = useState(false);
  const [previousSection, setPreviousSection] = useState<string | null>(null);
  const [forcedRedirection, setForcedRedirection] = useState(false);
  
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  
  // Check for new vouchers on mount and when refreshTrigger changes
  useEffect(() => {
    try {
      // Only count batches that haven't been received yet and are not from audit
      const pendingBatches = Array.isArray(voucherBatches) 
        ? voucherBatches.filter(batch => 
            batch && 
            !batch.received && 
            !batch.fromAudit && 
            batch.department === department
          )
        : [];
      
      setHasNewVouchers(pendingBatches.length > 0);
    } catch (error) {
      console.error('Error checking for new vouchers:', error);
      setHasNewVouchers(false);
    }
  }, [department, voucherBatches, refreshTrigger]);
  
  // Force redirection to new vouchers view
  const forceRedirectToNewVouchers = useCallback((currentSection: string) => {
    setPreviousSection(currentSection);
    setForcedRedirection(true);
  }, []);
  
  // Complete voucher reception and return to previous section
  const completeVoucherReception = useCallback(() => {
    setHasNewVouchers(false);
    setForcedRedirection(false);
    // Previous section will be used by the component to navigate back
  }, []);
  
  return {
    hasNewVouchers,
    previousSection,
    forcedRedirection,
    forceRedirectToNewVouchers,
    completeVoucherReception
  };
}
