import { useState, useEffect, useCallback, useRef } from 'react';
import { requestLock, releaseLock, getSocket, sendActivity, viewResource } from '@/lib/socket';
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';
import { ResourceViewer } from '@/lib/store/types';

interface UseLockOptions {
  autoRelease?: boolean;
  onLockAcquired?: () => void;
  onLockFailed?: () => void;
  onLockReleased?: () => void;
}

const INACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes in milliseconds

export function useResourceLock(
  resourceType: string,
  resourceId: string | null,
  options: UseLockOptions = {},
  targetDepartment?: string
) {
  const [isLocked, setIsLocked] = useState(false);
  const [isLockOwner, setIsLockOwner] = useState(false);
  const [lockOwner, setLockOwner] = useState<string | null>(null);
  const [viewers, setViewers] = useState<ResourceViewer[]>([]);
  const [viewerCount, setViewerCount] = useState(0);
  const [isViewing, setIsViewing] = useState(false);
  const [inactivityTimer, setInactivityTimer] = useState<NodeJS.Timeout | null>(null);

  const currentUser = useAppStore((state) => state.currentUser);
  const resourceLocks = useAppStore((state) => state.resourceLocks || []);
  const getResourceViewers = useAppStore((state) => state.getResourceViewers);
  const getResourceViewerCount = useAppStore((state) => state.getResourceViewerCount);

  // Default options
  const {
    autoRelease = true,
    onLockAcquired,
    onLockFailed,
    onLockReleased
  } = options;

  // Check if resource is locked
  useEffect(() => {
    if (!resourceId) return;

    const lockKey = `${resourceType}:${resourceId}`;
    const lock = resourceLocks.find((l) => l.key === lockKey);
    const users = useAppStore.getState().users;

    if (lock) {
      const wasLockedBySomeoneElse = isLocked && lockOwner && lockOwner !== currentUser?.id;
      const isNowLockedBySomeoneElse = lock.userId !== currentUser?.id;

      setIsLocked(true);
      setLockOwner(lock.userId);
      setIsLockOwner(currentUser?.id === lock.userId);

      // Show notification if lock ownership changed to someone else
      if (!wasLockedBySomeoneElse && isNowLockedBySomeoneElse) {
        const ownerName = users.find(u => u.id === lock.userId)?.name || 'Another user';
        toast({
          title: "Resource Locked",
          description: `${ownerName} is now editing this resource. You have read-only access.`,
          variant: "default",
        });
      }
    } else {
      const wasLockedBySomeoneElse = isLocked && lockOwner && lockOwner !== currentUser?.id;

      setIsLocked(false);

      // Show notification if lock was released by someone else
      if (wasLockedBySomeoneElse) {
        const previousOwnerName = users.find(u => u.id === lockOwner)?.name || 'Another user';
        toast({
          title: "Resource Unlocked",
          description: `${previousOwnerName} is no longer editing this resource. You can now edit it.`,
          variant: "default",
        });
      }

      setLockOwner(null);
      setIsLockOwner(false);
    }
  }, [resourceType, resourceId, resourceLocks, currentUser, isLocked, lockOwner]);

  // Listen for real-time lock updates
  useEffect(() => {
    if (!resourceId) return;

    const socket = getSocket();
    if (!socket) return;

    const handleLockUpdate = (data: any) => {
      // Get current user from store
      const currentUser = useAppStore.getState().currentUser;
      
      // Construct the lock key in the same way as the server
      const effectiveResourceId = currentUser?.department === 'AUDIT' && targetDepartment ?
        `${targetDepartment}-${resourceId}` : resourceId;
      const lockKey = `${resourceType}:${effectiveResourceId}`;

      if (data.key === lockKey) {
        console.log('Received lock update:', data);
        
        // Update local lock state immediately
        setIsLocked(!!data.isLocked);
        setLockOwner(data.userId || null);
        setIsLockOwner(currentUser?.id === data.userId);

        // Show toast notification for other users
        if (data.userId && data.userId !== currentUser?.id) {
          const users = useAppStore.getState().users;
          const ownerName = users.find(u => u.id === data.userId)?.name || 'Another user';
          
          if (data.isLocked) {
            toast({
              title: "Status Changed",
              description: `${ownerName} is now editing ${targetDepartment || 'this resource'}.`,
              variant: "default",
            });
          }
        } else if (!data.isLocked && data.previousUserId === currentUser?.id) {
          // Notify when lock is released
          toast({
            title: "Status Changed",
            description: `Resource is now OPENED.`,
            variant: "default",
          });
        }
      }
    };

    // Request initial lock state when mounting
    socket.emit('get_lock_state', {
      resourceType,
      resourceId,
      targetDepartment
    });

    socket.on('lock_update', handleLockUpdate);
    socket.on('lock_state', handleLockUpdate); // Handle initial state response

    // Also listen for disconnect/reconnect to refresh lock state
    const handleReconnect = () => {
      console.log('Socket reconnected, refreshing lock state');
      socket.emit('get_lock_state', {
        resourceType,
        resourceId,
        targetDepartment
      });
    };

    socket.on('reconnect', handleReconnect);

    return () => {
      socket.off('lock_update', handleLockUpdate);
      socket.off('lock_state', handleLockUpdate);
      socket.off('reconnect', handleReconnect);
    };
  }, [resourceId, resourceType, currentUser, targetDepartment]);

  // Listen for resource viewers updates
  useEffect(() => {
    if (!resourceId) return;

    const currentUser = useAppStore.getState().currentUser;
    
    // Construct the lock key in the same way as the server
    const effectiveResourceId = currentUser?.department === 'AUDIT' && targetDepartment ?
      `${targetDepartment}-${resourceId}` : resourceId;
    const lockKey = `${resourceType}:${effectiveResourceId}`;

    // Get initial viewers
    const initialViewers = getResourceViewers(lockKey);
    const initialViewerCount = getResourceViewerCount(lockKey);

    setViewers(initialViewers);
    setViewerCount(initialViewerCount);

    // Check if current user is already viewing
    const isCurrentUserViewing = initialViewers.some(v => v.userId === currentUser?.id);
    setIsViewing(isCurrentUserViewing);

    const socket = getSocket();
    if (!socket) return;

    const handleResourceViewers = (data: any) => {
      if (data.resourceKey === lockKey) {
        console.log('Received resource viewers update:', data);
        setViewers(data.viewers);
        setViewerCount(data.viewerCount);

        // Check if current user is viewing
        const isCurrentUserViewing = data.viewers.some((v: any) => v.userId === currentUser?.id);
        setIsViewing(isCurrentUserViewing);
      }
    };

    socket.on('resource_viewers', handleResourceViewers);

    return () => {
      socket.off('resource_viewers', handleResourceViewers);
    };
  }, [resourceType, resourceId, currentUser, getResourceViewers, getResourceViewerCount]);

  // Function to start the inactivity timer
  const startInactivityTimer = useCallback(() => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer);
    }

    const timer = setTimeout(async () => {
      if (isLockOwner) {
        console.log('Auto-releasing lock due to inactivity');
        await releaseLock(resourceType, resourceId, targetDepartment);
        setIsLocked(false);
        setIsLockOwner(false);
        setLockOwner(null);
        onLockReleased?.();
      }
    }, INACTIVITY_TIMEOUT);

    setInactivityTimer(timer);
  }, [resourceType, resourceId, targetDepartment, isLockOwner, onLockReleased]);

  // Reset the inactivity timer on any activity
  const resetInactivityTimer = useCallback(() => {
    if (isLockOwner) {
      startInactivityTimer();
      // Send activity to server to keep lock alive
      sendActivity(resourceType, resourceId, targetDepartment);
    }
  }, [resourceType, resourceId, targetDepartment, isLockOwner, startInactivityTimer]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (inactivityTimer) {
        clearTimeout(inactivityTimer);
      }
    };
  }, [inactivityTimer]);

  // Set up activity listeners
  useEffect(() => {
    if (isLockOwner) {
      // Start inactivity timer
      startInactivityTimer();

      // Add activity listeners
      const handleActivity = () => resetInactivityTimer();
      window.addEventListener('mousemove', handleActivity);
      window.addEventListener('keydown', handleActivity);
      window.addEventListener('click', handleActivity);
      window.addEventListener('scroll', handleActivity);

      return () => {
        // Clean up listeners
        window.removeEventListener('mousemove', handleActivity);
        window.removeEventListener('keydown', handleActivity);
        window.removeEventListener('click', handleActivity);
        window.removeEventListener('scroll', handleActivity);
      };
    }
  }, [isLockOwner, startInactivityTimer, resetInactivityTimer]);

  // Acquire lock
  const acquireLock = useCallback(async (): Promise<boolean> => {
    if (!resourceId || !currentUser) return false;

    try {
      // Log attempt
      console.log(`Attempting to acquire lock for ${resourceType}:${resourceId}${targetDepartment ? ` (${targetDepartment})` : ''}`);

      const success = await requestLock(resourceType, resourceId, targetDepartment);

      if (success) {
        console.log('Lock acquired successfully');
        setIsLocked(true);
        setLockOwner(currentUser.id);
        setIsLockOwner(true);
        onLockAcquired?.();

        // Start inactivity timer
        startInactivityTimer();

        // Mark as viewing when lock is acquired
        await viewResource(resourceType, resourceId, true, targetDepartment);
        setIsViewing(true);

        toast({
          title: "Editing Mode Activated",
          description: targetDepartment
            ? `You now have exclusive editing rights for ${targetDepartment} vouchers.`
            : "You now have exclusive editing rights for this resource.",
          variant: "default",
        });
      } else {
        console.log('Failed to acquire lock');
        onLockFailed?.();

        const lockKey = targetDepartment
          ? `${resourceType}:${targetDepartment}-${resourceId}`
          : `${resourceType}:${resourceId}`;
        const lock = resourceLocks.find(l => l.key === lockKey);
        const users = useAppStore.getState().users;
        const ownerName = lock ? (users.find(u => u.id === lock.userId)?.name || 'Another user') : 'Another user';

        toast({
          title: "Read-only Mode",
          description: targetDepartment
            ? `${ownerName} is currently editing ${targetDepartment} vouchers. You have read-only access.`
            : `${ownerName} is currently editing this resource. You have read-only access.`,
          variant: "default",
        });
      }

      return success;
    } catch (error) {
      console.error('Error acquiring lock:', error);
      onLockFailed?.();
      return false;
    }
  }, [resourceType, resourceId, currentUser, resourceLocks, onLockAcquired, onLockFailed, targetDepartment, startInactivityTimer]);

  // Release lock
  const releaseLockFn = useCallback(async (): Promise<boolean> => {
    if (!resourceId || !isLockOwner) return false;

    try {
      // Log with targetDepartment if provided
      if (targetDepartment) {
        console.log(`Releasing lock for ${resourceType}:${resourceId} (targeting ${targetDepartment})`);
      } else {
        console.log(`Releasing lock for ${resourceType}:${resourceId}`);
      }

      // Pass targetDepartment to releaseLock if provided
      const success = await releaseLock(resourceType, resourceId, targetDepartment);

      if (success) {
        // Log with targetDepartment if provided
        if (targetDepartment) {
          console.log(`Successfully released lock for ${resourceType}:${resourceId} (targeting ${targetDepartment})`);
        } else {
          console.log(`Successfully released lock for ${resourceType}:${resourceId}`);
        }

        setIsLocked(false);
        setLockOwner(null);
        setIsLockOwner(false);
        onLockReleased?.();

        // Show success toast with department-specific message if applicable
        toast({
          title: "Editing Mode Deactivated",
          description: targetDepartment
            ? `You have released editing rights for ${targetDepartment} vouchers. Other users can now edit them.`
            : "You have released editing rights. Other users can now edit this resource.",
          variant: "default",
        });

        // Mark as not viewing when lock is released
        await viewResource(resourceType, resourceId, false, targetDepartment);
        setIsViewing(false);
      } else {
        // Log with targetDepartment if provided
        if (targetDepartment) {
          console.log(`Failed to release lock for ${resourceType}:${resourceId} (targeting ${targetDepartment})`);
        } else {
          console.log(`Failed to release lock for ${resourceType}:${resourceId}`);
        }

        // Show failure toast
        toast({
          title: "Warning",
          description: "Failed to release editing rights. Please try again.",
          variant: "destructive",
        });
      }

      return success;
    } catch (error) {
      console.error('Error releasing lock:', error);

      toast({
        title: "Error",
        description: "Failed to release editing rights. Please try again.",
        variant: "destructive",
      });

      return false;
    }
  }, [resourceType, resourceId, isLockOwner, onLockReleased, targetDepartment]);

  // Get lock owner's name
  const users = useAppStore((state) => state.users);
  const lockOwnerName = lockOwner ? users.find(u => u.id === lockOwner)?.name || 'Unknown User' : null;

  return {
    isLocked,
    isLockOwner,
    lockOwner,
    lockOwnerName,
    acquireLock,
    releaseLock: releaseLockFn,
    // Viewer information
    viewers,
    viewerCount,
    isViewing,
    // Helper properties
    isEditable: !isLocked || isLockOwner,
    isViewOnly: isLocked && !isLockOwner,
    status: isLockOwner ? 'editor' : (isLocked ? 'viewer' : 'none'),
    resetInactivityTimer
  };
}
