
import { useEffect, useState } from 'react';
import { useAppStore } from '@/lib/store';
import { Voucher } from '@/lib/types';
import { formatCurrentDate } from '@/lib/store/utils';
import { toast } from 'sonner';

export function useVoucherProcessing(voucherId?: string, isFromBatch: boolean = false) {
  const currentUser = useAppStore(state => state.currentUser);
  const vouchers = useAppStore(state => state.vouchers);
  const updateVoucher = useAppStore(state => state.updateVoucher);

  const [voucher, setVoucher] = useState<Voucher | null>(null);
  const [rejectionComment, setRejectionComment] = useState('');
  const [showRejectionDialog, setShowRejectionDialog] = useState(false);

  useEffect(() => {
    if (voucherId) {
      // Use find method to get voucher instead of getVoucherById
      const foundVoucher = vouchers.find(v => v.id === voucherId);
      if (foundVoucher) {
        console.log(`Processing voucher ${voucherId} with status: ${foundVoucher.status}, isReturned: ${foundVoucher.isReturned}, pendingReturn: ${foundVoucher.pendingReturn}`);
        console.log(`Comment: "${foundVoucher.comment}", returnComment: "${foundVoucher.returnComment}"`);
        setVoucher(foundVoucher);
      }
    }
  }, [voucherId, vouchers]);

  const handleAccept = () => {
    if (!voucher) return;

    const currentDate = formatCurrentDate();
    console.log(`Accepting voucher ${voucher.id}, isReturned: ${voucher.isReturned}, pendingReturn: ${voucher.pendingReturn}`);

    try {
      // Check if this is a returned voucher (isReturned or pendingReturn or has VOUCHER RETURNED status)
      if (voucher.isReturned || voucher.pendingReturn || voucher.status === "VOUCHER RETURNED") {
        console.log(`Processing RETURNED voucher ${voucher.id}`);
        console.log(`Current comment: "${voucher.comment}", returnComment: "${voucher.returnComment}"`);
        console.log(`Current status: ${voucher.status}, isReturned: ${voucher.isReturned}, pendingReturn: ${voucher.pendingReturn}`);

        // Make sure comments are preserved
        const commentText = voucher.returnComment || voucher.comment || "NO COMMENT PROVIDED";

        // Update voucher status for returned vouchers
        const updateData = {
          departmentReceiptTime: currentDate,
          departmentReceivedBy: currentUser?.name || "Unknown User",
          status: "VOUCHER RETURNED",
          isReturned: true,  // Ensure isReturned flag is explicitly set
          pendingReturn: false, // Clear pendingReturn flag
          returnComment: String(commentText), // Ensure comment is preserved and is a string
          comment: String(commentText), // Set both comment fields
          dispatched: true    // Ensure it's marked as dispatched
        };

        console.log(`Updating returned voucher ${voucher.id} with:`, updateData);

        updateVoucher(voucher.id, updateData);

        toast.success(`RETURNED VOUCHER ${voucher.voucherId} ACCEPTED`, {
          duration: 3000,
        });
      }
      // For rejected vouchers
      else if (voucher.status === "VOUCHER REJECTED") {
        console.log(`Processing REJECTED voucher ${voucher.id}`);
        console.log(`Current comment: "${voucher.comment}"`);

        updateVoucher(voucher.id, {
          departmentReceiptTime: currentDate,
          departmentReceivedBy: currentUser?.name || "Unknown User",
          comment: String(voucher.comment || "NO COMMENT PROVIDED") // Ensure comment is preserved and is a string
        });

        toast.success(`REJECTED VOUCHER ${voucher.voucherId} ACCEPTED`, {
          duration: 3000,
        });
      }
      // For certified vouchers
      else if (voucher.status === "VOUCHER CERTIFIED") {
        console.log(`Processing CERTIFIED voucher ${voucher.id}`);

        updateVoucher(voucher.id, {
          departmentReceiptTime: currentDate,
          departmentReceivedBy: currentUser?.name || "Unknown User",
        });

        toast.success(`CERTIFIED VOUCHER ${voucher.voucherId} ACCEPTED`, {
          duration: 3000,
        });
      }
    } catch (error) {
      console.error("Error accepting voucher:", error);
      toast.error(`FAILED TO ACCEPT VOUCHER ${voucher.voucherId}`, {
        duration: 3000,
      });
    }
  };

  const handleReject = () => {
    if (!voucher) return;
    setShowRejectionDialog(true);
  };

  const handleConfirmRejection = (voucherId: string, comment: string) => {
    if (!voucher) return;

    const currentDate = formatCurrentDate();

    try {
      // Ensure comment is a string
      const commentText = String(comment || "NO COMMENT PROVIDED");
      console.log(`Rejecting voucher ${voucherId} with comment: "${commentText}"`);

      // Check if this is a returned voucher
      const isReturnedVoucher = voucher.isReturned || voucher.pendingReturn;
      console.log(`Rejecting voucher ${voucherId}, isReturnedVoucher: ${isReturnedVoucher}`);

      // Use different handling for returned vouchers vs regular vouchers
      if (isReturnedVoucher) {
        updateVoucher(voucherId, {
          departmentReceiptTime: currentDate,
          departmentReceivedBy: currentUser?.name || "Unknown User",
          // Store comment as plain string for returned vouchers in both fields
          returnComment: commentText,
          comment: commentText,
          status: "VOUCHER RETURNED",  // Keep as returned
          isReturned: true,
          pendingReturn: false,
          departmentRejected: true
        });

        toast.success(`RETURNED VOUCHER ${voucher.voucherId} REJECTED`, {
          duration: 3000,
        });
      } else {
        updateVoucher(voucherId, {
          departmentReceiptTime: currentDate,
          departmentReceivedBy: currentUser?.name || "Unknown User",
          comment: commentText, // Use comment instead of departmentRejectionReason
          departmentRejected: true
        });

        toast.success(`VOUCHER ${voucher.voucherId} REJECTED`, {
          duration: 3000,
        });
      }

      setShowRejectionDialog(false);
    } catch (error) {
      console.error("Error rejecting voucher:", error);
      toast.error(`FAILED TO REJECT VOUCHER ${voucher.voucherId}`, {
        duration: 3000,
      });
    }
  };

  return {
    voucher,
    rejectionComment,
    showRejectionDialog,
    setRejectionComment,
    setShowRejectionDialog,
    handleAccept,
    handleReject,
    handleConfirmRejection,
    isReturnedVoucher: voucher?.isReturned || voucher?.pendingReturn || false
  };
}
