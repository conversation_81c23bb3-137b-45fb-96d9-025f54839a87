import axios from 'axios';

// Create axios instance with base URL and default headers
const api = axios.create({
  // Use a relative path for the API endpoint to work in any environment
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  },
});

// CRITICAL FIX: Add response interceptor to handle session expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Check if the error is due to an expired session or user already logged in
    if (error.response &&
        (error.response.status === 401 ||
         (error.response.status === 403 && error.response.data.error === 'User already logged in'))) {
      console.error('Session error:', error.response.data);

      // Clear token and redirect to login page
      localStorage.removeItem('auth_token');

      // Show alert with the error message
      const message = error.response.data.message ||
                     (error.response.status === 401 ? 'Your session has expired. Please log in again.' :
                                                     'You are already logged in from another device.');
      alert(message);

      // Redirect to login page
      window.location.href = '/';
    }

    return Promise.reject(error);
  }
);

// Add request interceptor to add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  login: async (department: string, username: string, password: string) => {
    const response = await api.post('/auth/login', { department, username, password });
    return response.data;
  },
  register: async (name: string, password: string, department: string) => {
    const response = await api.post('/auth/register', { name, password, department });
    return response.data;
  },
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },
  logout: async () => {
    const response = await api.post('/auth/logout');
    localStorage.removeItem('auth_token');
    return response.data;
  },
};

// Users API
export const usersApi = {
  getAllUsers: async () => {
    // Add timestamp to prevent caching
    const timestamp = new Date().getTime();
    const response = await api.get(`/users?_t=${timestamp}`);
    return response.data;
  },
  getUserById: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },
  createUser: async (userData: any) => {
    const response = await api.post('/users', userData);
    return response.data;
  },
  updateUser: async (id: string, userData: any) => {
    // Ensure isActive is properly formatted for the server
    if (userData.isActive !== undefined) {
      // Convert to boolean and ensure it's sent as a boolean value
      userData = {
        ...userData,
        isActive: Boolean(userData.isActive)
      };
      console.log(`Sending isActive as ${userData.isActive} (${typeof userData.isActive})`);
    }

    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },
  changePassword: async (id: string, currentPassword: string, newPassword: string) => {
    const response = await api.put(`/users/${id}/password`, { currentPassword, newPassword });
    return response.data;
  },
  resetPassword: async (id: string, newPassword: string) => {
    const response = await api.put(`/users/${id}/reset-password`, { newPassword });
    return response.data;
  },
  deleteUser: async (id: string) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  },
  getPendingRegistrations: async () => {
    // Add timestamp to prevent caching
    const timestamp = new Date().getTime();
    const response = await api.get(`/users/registrations/pending?_t=${timestamp}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    console.log(`Fetched ${response.data.length} pending registrations from API`);
    return response.data;
  },
  approveRegistration: async (id: string) => {
    const response = await api.post(`/users/registrations/${id}/approve`);
    return response.data;
  },
  rejectRegistration: async (id: string) => {
    const response = await api.post(`/users/registrations/${id}/reject`);
    return response.data;
  },
};

// Vouchers API
export const vouchersApi = {
  getAllVouchers: async (department?: string) => {
    const params: any = department ? { department } : {};
    console.log(`Fetching vouchers with params:`, params);

    // Add a timestamp to prevent caching
    params.timestamp = new Date().getTime();

    try {
      console.log(`Fetching vouchers from API for department: ${department || 'all'} at ${new Date().toISOString()}`);

      const response = await api.get('/vouchers', {
        params,
        // Add cache control headers
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      console.log(`Received ${response.data.length} vouchers from API for department: ${department || 'all'}`);

      // Check if we have any vouchers
      if (response.data.length === 0) {
        console.warn(`No vouchers returned for department: ${department || 'all'}`);
      }

      // Log the first few vouchers for debugging
      if (response.data.length > 0) {
        console.log('First few vouchers:', response.data.slice(0, 3).map((v: any) => ({
          id: v.id,
          voucherId: v.voucher_id,
          department: v.department,
          status: v.status
        })));

        // Check for missing voucher IDs (gaps in sequence)
        const voucherIds = response.data
          .map((v: any) => v.voucher_id)
          .filter(Boolean)
          .sort();

        if (voucherIds.length > 1) {
          // Group by month prefix
          const vouchersByPrefix: Record<string, string[]> = {};
          voucherIds.forEach((id: string) => {
            const prefix = id.substring(0, 3);
            if (!vouchersByPrefix[prefix]) vouchersByPrefix[prefix] = [];
            vouchersByPrefix[prefix].push(id);
          });

          // Check for gaps in each prefix group
          Object.entries(vouchersByPrefix).forEach(([prefix, ids]) => {
            if (ids.length > 1) {
              // Extract numeric parts and check for gaps
              const numericParts = ids.map(id => parseInt(id.substring(3)));
              numericParts.sort((a, b) => a - b);

              // Check for gaps
              for (let i = 1; i < numericParts.length; i++) {
                if (numericParts[i] - numericParts[i-1] > 1) {
                  console.warn(`Gap detected in voucher sequence: ${prefix}${numericParts[i-1].toString().padStart(5, '0')} -> ${prefix}${numericParts[i].toString().padStart(5, '0')}`);
                }
              }

              // Check if the first voucher is missing
              if (numericParts[0] > 1) {
                console.warn(`First voucher may be missing! First voucher in sequence is ${prefix}${numericParts[0].toString().padStart(5, '0')}`);
              }
            }
          });
        }
      }

      // Map server response to client model to ensure consistent property names
      const mappedVouchers = response.data.map((v: any) => ({
        id: v.id,
        voucherId: v.voucher_id,
        date: v.date,
        claimant: v.claimant,
        description: v.description,
        amount: v.amount,
        currency: v.currency,
        department: v.department,
        dispatchedBy: v.dispatched_by,
        dispatchTime: v.dispatch_time,
        status: v.status,
        sentToAudit: v.sent_to_audit,
        createdBy: v.created_by,
        comment: v.comment,
        returnComment: v.return_comment,
        isReturned: v.is_returned,
        pendingReturn: v.pending_return,
        deleted: v.deleted,
        // Include all other fields
        ...v
      }));

      // Log success
      console.log(`Successfully mapped ${mappedVouchers.length} vouchers for department: ${department || 'all'}`);

      return mappedVouchers;
    } catch (error) {
      console.error(`Error fetching vouchers for department ${department || 'all'}:`, error);
      throw error;
    }
  },
  getVoucherById: async (id: string) => {
    const response = await api.get(`/vouchers/${id}`);
    return response.data;
  },
  createVoucher: async (voucherData: any) => {
    const response = await api.post('/vouchers', voucherData);
    return response.data;
  },
  updateVoucher: async (id: string, voucherData: any) => {
    const response = await api.put(`/vouchers/${id}`, voucherData);
    return response.data;
  },
  deleteVoucher: async (id: string) => {
    const response = await api.delete(`/vouchers/${id}`);
    return response.data;
  },
  sendToAudit: async (id: string) => {
    const response = await api.post(`/vouchers/${id}/send-to-audit`);
    return response.data;
  },
  returnVoucher: async (id: string, returnComment: string) => {
    const response = await api.post(`/vouchers/${id}/return`, { returnComment });
    return response.data;
  },
  certifyVoucher: async (id: string) => {
    const response = await api.post(`/vouchers/${id}/certify`);
    return response.data;
  },
  rejectVoucher: async (id: string, comment: string) => {
    const response = await api.post(`/vouchers/${id}/reject`, { comment });
    return response.data;
  },
  getBlacklistedVoucherIds: async () => {
    const response = await api.get('/vouchers/blacklist/ids');
    return response.data;
  },
  addBlacklistedVoucherId: async (voucherId: string) => {
    const response = await api.post('/vouchers/blacklist/ids', { voucherId });
    return response.data;
  },
};

// Batches API
export const batchesApi = {
  getAllBatches: async (department?: string) => {
    const params = department ? { department } : {};
    const response = await api.get('/batches', { params });
    return response.data;
  },
  getBatchById: async (id: string) => {
    const response = await api.get(`/batches/${id}`);
    return response.data;
  },
  createBatch: async (batchData: any) => {
    const response = await api.post('/batches', batchData);
    return response.data;
  },
  receiveBatch: async (id: string, receivedVoucherIds: string[], rejectedVoucherIds: string[], rejectionComments: Record<string, string> = {}) => {
    const response = await api.post(`/batches/${id}/receive`, { receivedVoucherIds, rejectedVoucherIds, rejectionComments });
    return response.data;
  },
};

// Provisional Cash API
export const provisionalCashApi = {
  getAllRecords: async (department?: string) => {
    const params = department ? { department } : {};
    const response = await api.get('/provisional-cash', { params });
    return response.data;
  },
  getRecordById: async (id: string) => {
    const response = await api.get(`/provisional-cash/${id}`);
    return response.data;
  },
  createRecord: async (recordData: any) => {
    const response = await api.post('/provisional-cash', recordData);
    return response.data;
  },
  updateRecord: async (id: string, recordData: any) => {
    const response = await api.put(`/provisional-cash/${id}`, recordData);
    return response.data;
  },
  deleteRecord: async (id: string) => {
    const response = await api.delete(`/provisional-cash/${id}`);
    return response.data;
  },
};

// Notifications API
export const notificationsApi = {
  getAllNotifications: async () => {
    const response = await api.get('/notifications');
    return response.data;
  },
  getUnreadCount: async () => {
    const response = await api.get('/notifications/unread/count');
    return response.data;
  },
  markAsRead: async (id: string) => {
    const response = await api.put(`/notifications/${id}/read`);
    return response.data;
  },
  markAllAsRead: async () => {
    const response = await api.put('/notifications/read/all');
    return response.data;
  },
  createNotification: async (notificationData: any) => {
    const response = await api.post('/notifications', notificationData);
    return response.data;
  },
  deleteNotification: async (id: string) => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  },
};

// Admin API
export const adminApi = {
  getSystemSettings: async () => {
    const response = await api.get('/admin/settings');
    return response.data;
  },
  updateSystemSettings: async (settingsData: any) => {
    const response = await api.put('/admin/settings', settingsData);
    return response.data;
  },
  backupDatabase: async () => {
    const response = await api.post('/admin/backup');
    return response.data;
  },
  restoreDatabase: async (filename: string) => {
    const response = await api.post('/admin/restore', { filename });
    return response.data;
  },
  getBackups: async () => {
    const response = await api.get('/admin/backups');
    return response.data;
  },
  getAuditLogs: async (params: any = {}) => {
    const response = await api.get('/admin/audit-logs', { params });
    return response.data;
  },
};

// Audit API
export const auditApi = {
  getDashboardData: async () => {
    const response = await api.get('/audit/dashboard');
    return response.data;
  },
  getAnalyticsData: async (params: any = {}) => {
    const response = await api.get('/audit/analytics', { params });
    return response.data;
  },
  logAuditAction: async (actionData: any) => {
    const response = await api.post('/audit/log', actionData);
    return response.data;
  },
  getResourceLogs: async (resourceType: string, resourceId: string) => {
    const response = await api.get(`/audit/logs/${resourceType}/${resourceId}`);
    return response.data;
  },
};

export default {
  auth: authApi,
  users: usersApi,
  vouchers: vouchersApi,
  batches: batchesApi,
  provisionalCash: provisionalCashApi,
  notifications: notificationsApi,
  admin: adminApi,
  audit: auditApi,
};
