import { ClearanceRemark, Currency, Department, Notification, ProvisionalCashRecord, TaxType, TransactionStatus, User, Voucher } from "./types";

// Mock data for development
export const departments: Department[] = [
  "FINANCE",
  "MINISTRIES",
  "PENSIONS",
  "PENTMEDIA",
  "MISSIONS",
  "PENTSOS",
  "AUDIT",
  "SYSTEM ADMIN"
];

export const taxTypes: TaxType[] = [
  "GOODS 3%",
  "SERVICE 7.5%",
  "WORKS 5%",
  "RENT 8%",
  "PCC 12.5%",
  "RISK 5%",
  "VEH.MAINT 10%",
  "OTHER"
];

export const currencies: Currency[] = [
  "GHS",
  "USD",
  "GBP",
  "EUR"
];

// Helper function to ensure all users have the required fields
const createCompleteUser = (user: Partial<User>): User => {
  // Default password is department name + 123 (e.g., FINANCE123)
  const defaultPassword = `${user.department}123`;
  
  return {
    id: user.id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    name: user.name || '',
    department: user.department || '',
    role: user.role || 'USER',
    email: `${user.name?.toLowerCase().replace(/\s+/g, '.')}@${user.department?.toLowerCase()}.com`,
    password: user.password || defaultPassword,
    dateCreated: new Date().toISOString(),
    isActive: true,
    ...user
  } as User;
};

// Mock users
export const users: User[] = [
  // FINANCE Department
  createCompleteUser({ id: "f1", name: "FELIX AYISI", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f2", name: "CHRISTABEL TAMIA", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f3", name: "EBENZER A. MENSAH", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f4", name: "FRANKLIN POKU", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f5", name: "KWASI AFRAM", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f6", name: "JAMES NABEL", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f7", name: "ANTWI BAFFOE", department: "FINANCE", role: "USER" }),
  createCompleteUser({ id: "f8", name: "GEORGINA OFORI", department: "FINANCE", role: "USER" }),

  // MISSIONS Department
  createCompleteUser({ id: "m1", name: "CHARIS A.N. OBENG", department: "MISSIONS", role: "USER" }),
  createCompleteUser({ id: "m2", name: "JONATHAN JOWEL", department: "MISSIONS", role: "USER" }),
  createCompleteUser({ id: "m3", name: "TRUST YAWEH K.", department: "MISSIONS", role: "USER" }),
  createCompleteUser({ id: "m4", name: "DAVID ASARE MARFO", department: "MISSIONS", role: "USER" }),

  // PENSIONS Department
  createCompleteUser({ id: "p1", name: "JERRY-JOHN NAMPARI YENABI", department: "PENSIONS", role: "USER" }),

  // MINISTRIES Department
  createCompleteUser({ id: "mi1", name: "SAMUEL MAWUKO. A.", department: "MINISTRIES", role: "USER" }),
  createCompleteUser({ id: "mi2", name: "JAMES NABEL", department: "MINISTRIES", role: "USER" }),
  createCompleteUser({ id: "mi3", name: "ANTWI BAFFOE", department: "MINISTRIES", role: "USER" }),

  // PENTMEDIA Department
  createCompleteUser({ id: "pm1", name: "PATIENCE TWENEWAA EFFAH", department: "PENTMEDIA", role: "USER" }),

  // PENTSOS Department
  createCompleteUser({ id: "ps1", name: "GYAMPOH JOSEPH ODOSU", department: "PENTSOS", role: "USER" }),
  createCompleteUser({ id: "ps2", name: "FRANK ADUTWUM", department: "PENTSOS", role: "USER" }),
  createCompleteUser({ id: "ps3", name: "ALEXANDER K. FRIMPONG", department: "PENTSOS", role: "USER" }),
  createCompleteUser({ id: "ps4", name: "MICHAEL KUMI", department: "PENTSOS", role: "USER" }),
  createCompleteUser({ id: "ps5", name: "EUNICE A. PUMPUNI", department: "PENTSOS", role: "USER" }),

  // AUDIT Department
  createCompleteUser({ id: "a1", name: "FIRANG BOAKYE", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a2", name: "HARRISON A. SARPONG", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a3", name: "WILLIAM AKUAMOAH", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a4", name: "RICHARD ARTHUR", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a5", name: "SAMUEL ASIEDU", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a6", name: "SAMUEL CATOE", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a7", name: "BRIGHT OMANE", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a8", name: "KWAME A. FRIMPNG", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a9", name: "ABENA Y. FORSON", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a10", name: "ELIZABETH A. KWAKYE", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a11", name: "NAOMI O. ANSAH", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a12", name: "AKUA BRIFAA", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a13", name: "SYLVESTER M. NYARKO", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a14", name: "DEBORAH ANIM", department: "AUDIT", role: "USER" }),
  createCompleteUser({ id: "a15", name: "PRINCE O. BONSU", department: "AUDIT", role: "USER" }),

  // ADMINISTRATOR
  createCompleteUser({
    id: "admin1",
    name: "ADMIN",
    department: "SYSTEM ADMIN",
    role: "admin"
  }),
  
  // Add default users for each department with standard naming
  createCompleteUser({
    id: "finance-user-1",
    name: "FINANCE_USER",
    department: "FINANCE",
    password: "department123",
    role: "USER"
  }),
  createCompleteUser({
    id: "audit-user-1",
    name: "AUDIT_USER",
    department: "AUDIT",
    password: "department123",
    role: "USER"
  }),
  createCompleteUser({
    id: "ministries-user-1",
    name: "MINISTRIES_USER",
    department: "MINISTRIES",
    password: "department123",
    role: "USER"
  }),
  createCompleteUser({
    id: "pensions-user-1",
    name: "PENSIONS_USER",
    department: "PENSIONS",
    password: "department123",
    role: "USER"
  }),
  createCompleteUser({
    id: "pentmedia-user-1",
    name: "PENTMEDIA_USER",
    department: "PENTMEDIA",
    password: "department123",
    role: "USER"
  }),
  createCompleteUser({
    id: "missions-user-1",
    name: "MISSIONS_USER",
    department: "MISSIONS",
    password: "department123",
    role: "USER"
  }),
  createCompleteUser({
    id: "pentsos-user-1",
    name: "PENTSOS_USER",
    department: "PENTSOS",
    password: "department123",
    role: "USER"
  })
];

// Initial vouchers for development
export const initialVouchers: Voucher[] = [
  {
    id: "v1",
    voucherId: "APR00001",
    date: "28-APRIL-2025 10:00 AM",
    claimant: "John Doe",
    description: "Office Supplies Purchase",
    amount: 5000,
    currency: "GHS",
    department: "FINANCE",
    dispatchedBy: "Finance Officer",
    dispatchTime: "28-APRIL-2025 10:30 AM",
    status: "PENDING SUBMISSION",
    sentToAudit: false
  },
  {
    id: "v2",
    voucherId: "APR00002",
    date: "28-APRIL-2025 11:00 AM",
    claimant: "Jane Smith",
    description: "Transportation Allowance",
    amount: 3000,
    currency: "GHS",
    department: "MINISTRIES",
    dispatchedBy: "Ministries Manager",
    dispatchTime: "28-APRIL-2025 11:15 AM",
    status: "VOUCHER CERTIFIED",
    sentToAudit: true,
    receivedBy: "Audit Officer",
    receiptTime: "28-APRIL-2025 11:45 AM",
    certifiedBy: "Audit Officer",
    auditDispatchTime: "28-APRIL-2025 12:30 PM",
    auditDispatchedBy: "Audit Officer",
    preAuditedAmount: 3000
  },
  {
    id: "v3",
    voucherId: "APR00003",
    date: "28-APRIL-2025 12:00 PM",
    claimant: "Alice Johnson",
    description: "Pension Payment",
    amount: 12000,
    currency: "GHS",
    department: "PENSIONS",
    dispatchedBy: "Pensions Officer",
    dispatchTime: "28-APRIL-2025 12:15 PM",
    status: "VOUCHER REJECTED",
    sentToAudit: true,
    receivedBy: "Audit Officer",
    receiptTime: "28-APRIL-2025 12:45 PM",
    comment: "Missing supporting documents"
  }
];

// Initial provisional cash records
export const initialProvisionalCashRecords: ProvisionalCashRecord[] = [
  {
    id: "pcr1",
    voucherId: "v2",
    voucherRef: "APR00002",
    claimant: "Jane Smith",
    description: "Transportation Allowance",
    mainAmount: 3000,
    currency: "GHS",
    amountRetired: 3000,
    clearanceRemark: "CLEARED",
    dateRetired: "APR 29, 2025 AT 10:00AM",
    clearedBy: "Audit Officer",
    comment: "All receipts verified",
    date: "APR 28, 2025 AT 12:00AM"
  }
];

// Initial notifications
export const initialNotifications: Notification[] = [
  {
    id: "n1",
    userId: "7",
    message: "New voucher received from Finance Department",
    isRead: false,
    timestamp: "28-APRIL-2025 10:30 AM",
    voucherId: "v1",
    type: "NEW_VOUCHER"
  },
  {
    id: "n2",
    userId: "1",
    message: "Voucher APR00003 rejected by Audit",
    isRead: false,
    timestamp: "28-APRIL-2025 12:45 PM",
    voucherId: "v3",
    type: "VOUCHER_REJECTED"
  }
];

// Generate voucher ID - using month prefix and sequential numbering per department
export const generateVoucherId = (department: string): string => {
  const date = new Date();
  const month = date.toLocaleString('default', { month: 'short' }).toUpperCase();
  const currentMonthPrefix = month.substring(0, 3);

  // Get all vouchers with the current month prefix for this department
  const vouchersForDepartment = initialVouchers.filter(v =>
    v.voucherId.startsWith(currentMonthPrefix) && v.department === department
  );

  // Find the highest sequential number for the current month and department
  let maxSequentialNumber = 0;
  vouchersForDepartment.forEach(voucher => {
    const numericPart = voucher.voucherId.substring(3);
    const sequentialNumber = parseInt(numericPart, 10);
    if (!isNaN(sequentialNumber) && sequentialNumber > maxSequentialNumber) {
      maxSequentialNumber = sequentialNumber;
    }
  });

  // Generate new sequential number for the current month and department
  const newSequentialNumber = maxSequentialNumber + 1;
  return `${currentMonthPrefix}${newSequentialNumber.toString().padStart(4, '0')}`;
};

// Calculate clearance remark
export type ClearanceResult = {
  remark: ClearanceRemark;
  difference: number;
}

export const calculateClearanceRemark = (mainAmount: number, amountRetired: number): ClearanceResult => {
  const difference = Math.abs(mainAmount - amountRetired);

  if (mainAmount === amountRetired) {
    return { remark: "CLEARED", difference: 0 };
  } else if (amountRetired > mainAmount) {
    return { remark: "DUE STAFF", difference };
  } else {
    return { remark: "REFUNDED TO CHEST", difference };
  }
};
