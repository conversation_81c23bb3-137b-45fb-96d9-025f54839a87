import { io, Socket } from 'socket.io-client';
import { useAppStore } from './store';

// Socket.io client instance
let socket: Socket | null = null;

// Track connection status
let isConnected = false;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 30;
const RECONNECT_DELAY = 1000;
const HEARTBEAT_INTERVAL = 30000; // 30 seconds

// Track resource viewing and editing
let inactivityTimer: NodeJS.Timeout | null = null;
const INACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes

// Connect to WebSocket server
export const connectSocket = (token: string): Socket => {
  console.log('Connecting to WebSocket server...');

  // Close existing socket if it exists
  if (socket) {
    console.log('Closing existing socket connection before creating a new one');
    socket.disconnect();
  }

  // Create new socket connection with robust reconnection settings
  socket = io(process.env.NEXT_PUBLIC_WS_URL || '/', {
    auth: { token },
    reconnection: true,
    reconnectionAttempts: MAX_RECONNECT_ATTEMPTS,
    reconnectionDelay: RECONNECT_DELAY,
    reconnectionDelayMax: 5000,
    timeout: 30000,
    // Add CORS settings
    withCredentials: false,
    // Add additional options for better reliability
    forceNew: true,
    transports: ['websocket', 'polling'],
    // Add additional connection options for stability
    autoConnect: true,
    rejectUnauthorized: false,
    secure: true,
    closeOnBeforeunload: false,
    // Add path configuration
    path: '/socket.io',
    // Add query parameters for better tracking
    query: {
      clientId: Date.now().toString(),
      timestamp: new Date().toISOString()
    }
  });

  console.log('Socket connection initialized with token and path configuration');

  // Setup event listeners
  setupSocketListeners(socket);

  // Setup reconnection handling
  socket.on('reconnect_attempt', (attemptNumber) => {
    console.log(`Attempting to reconnect (${attemptNumber}/${MAX_RECONNECT_ATTEMPTS})...`);
    reconnectAttempts = attemptNumber;
  });

  socket.on('reconnect', (attemptNumber) => {
    console.log(`Reconnected after ${attemptNumber} attempts`);
    isConnected = true;
    reconnectAttempts = 0;
    
    // Rejoin department room
    const store = useAppStore.getState();
    if (store.user?.department) {
      socket.emit('join_department', {
        department: store.user.department,
        userId: store.user.id,
        userName: store.user.name
      });
    }
  });

  socket.on('reconnect_error', (error) => {
    console.error('Reconnection error:', error);
  });

  socket.on('reconnect_failed', () => {
    console.error('Failed to reconnect after maximum attempts');
    isConnected = false;
  });

  // Setup heartbeat
  const heartbeatInterval = setInterval(() => {
    if (socket?.connected) {
      socket.emit('heartbeat');
    }
  }, HEARTBEAT_INTERVAL);

  // Cleanup on disconnect
  socket.on('disconnect', (reason) => {
    console.log('Socket disconnected:', reason);
    isConnected = false;
    clearInterval(heartbeatInterval);
  });

  return socket;
};

// Disconnect from WebSocket server
export const disconnectSocket = (): void => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

// Get current socket instance
export const getSocket = (): Socket | null => {
  return socket;
};

// Setup socket event listeners
const setupSocketListeners = (socket: Socket): void => {
  // Handle connection events
  socket.on('connect', () => {
    console.log('Connected to WebSocket server with ID:', socket.id);
    isConnected = true;
    reconnectAttempts = 0;

    // Clear any existing reconnection error messages
    if (window.toast) {
      window.toast({
        title: "Connected",
        description: "Successfully connected to server",
        variant: "default",
      });
    }

    // Get current user from store
    const currentUser = useAppStore.getState().currentUser;
    console.log('Current user on socket connect:', currentUser ? `${currentUser.name} (${currentUser.department})` : 'Not logged in');

    // CRITICAL FIX: Explicitly join department room and announce presence
    if (currentUser) {
      console.log(`Explicitly joining department:${currentUser.department} room`);
      socket.emit('join_department', {
        department: currentUser.department,
        userId: currentUser.id,
        userName: currentUser.name
      });

      // Also announce presence to other users
      socket.emit('announce_presence', {
        userId: currentUser.id,
        userName: currentUser.name,
        department: currentUser.department
      });
    }

    // Force a refresh of all data to ensure we have the latest
    if (currentUser) {
      // Fetch vouchers for the current department
      useAppStore.getState().fetchVouchers(currentUser.department).then(() => {
        console.log('Refreshed vouchers after socket connection');
      });

      // Fetch users to update active users display
      useAppStore.getState().fetchAllUsers().then(() => {
        console.log('Refreshed users after socket connection');
      });

      // Fetch notifications
      useAppStore.getState().fetchNotifications().then(() => {
        console.log('Refreshed notifications after socket connection');
      });

      // Show toast notification for successful connection
      if (window.toast) {
        window.toast({
          title: "Connected",
          description: `Connected to server as ${currentUser.name}`,
          variant: "default",
        });
      }
    }

    // Start heartbeat
    startHeartbeat();
  });

  socket.on('disconnect', (reason) => {
    console.log(`Disconnected from WebSocket server: ${reason}`);
    isConnected = false;

    // Show toast notification for disconnection
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser && window.toast) {
      window.toast({
        title: "Disconnected",
        description: `Lost connection to server. Attempting to reconnect...`,
        variant: "destructive",
      });
    }

    // Attempt to reconnect if not already reconnecting
    if (reason === 'io server disconnect' || reason === 'io client disconnect') {
      console.log('Attempting to reconnect...');
      socket.connect();
    }

    // Stop heartbeat
    stopHeartbeat();
  });

  socket.on('reconnect', (attemptNumber) => {
    console.log(`Reconnected to WebSocket server after ${attemptNumber} attempts`);
    isConnected = true;
    reconnectAttempts = 0;

    // Show toast notification for reconnection
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser && window.toast) {
      window.toast({
        title: "Reconnected",
        description: `Reconnected to server after ${attemptNumber} attempts`,
        variant: "default",
      });

      // Force refresh all data
      useAppStore.getState().fetchVouchers(currentUser.department);
      useAppStore.getState().fetchAllUsers();
      useAppStore.getState().fetchNotifications();
    }

    // Restart heartbeat
    startHeartbeat();
  });

  socket.on('reconnect_attempt', (attemptNumber) => {
    reconnectAttempts = attemptNumber;
    console.log(`Attempting to reconnect to WebSocket server (attempt ${attemptNumber} of ${MAX_RECONNECT_ATTEMPTS})`);

    // Show toast notification for reconnection attempt if it's been a while
    if (attemptNumber % 5 === 0) { // Show every 5 attempts
      const currentUser = useAppStore.getState().currentUser;
      if (currentUser && window.toast) {
        window.toast({
          title: "Reconnecting",
          description: `Attempting to reconnect to server (${attemptNumber}/${MAX_RECONNECT_ATTEMPTS})`,
          variant: "default",
        });
      }
    }
  });

  socket.on('reconnect_error', (error) => {
    console.error('Error reconnecting to WebSocket server:', error);
  });

  socket.on('reconnect_failed', () => {
    console.error('Failed to reconnect to WebSocket server after maximum attempts');

    // Show toast notification for reconnection failure
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser && window.toast) {
      window.toast({
        title: "Connection Failed",
        description: `Failed to reconnect to server after ${MAX_RECONNECT_ATTEMPTS} attempts. Please refresh the page.`,
        variant: "destructive",
      });
    }
  });

  socket.on('connect_error', (error) => {
    console.error('WebSocket connection error:', error);

    // Try to reconnect after a delay
    setTimeout(() => {
      console.log('Attempting to reconnect after connection error...');
      socket.connect();
    }, 5000);
  });

  // Handle lock updates
  socket.on('locks_update', (data) => {
    console.log('Received locks update:', data);
    // Update locks in store
    const store = useAppStore.getState();
    if (store.updateResourceLocks) {
      store.updateResourceLocks(data.locks);
    }
  });

  socket.on('lock_update', (data) => {
    console.log('Received lock update:', data);
    // Update lock in store
    const store = useAppStore.getState();
    if (store.updateResourceLock) {
      store.updateResourceLock(data.key, data.isLocked, data.userId);
    }
  });

  // Handle state updates
  socket.on('state_update', (data) => {
    console.log('Received state update:', data);

    // Update store based on update type
    const store = useAppStore.getState();

    switch (data.type) {
      case 'VOUCHER_UPDATE':
        if (store.updateVoucher && data.voucher) {
          store.updateVoucher(data.voucher.id, data.voucher);
        }
        break;

      case 'VOUCHER_CREATE':
        if (store.addVoucher && data.voucher) {
          store.addVoucher(data.voucher);
        }
        break;

      case 'VOUCHER_DELETE':
        if (store.deleteVoucher && data.voucherId) {
          store.deleteVoucher(data.voucherId);
        }
        break;

      case 'BATCH_UPDATE':
        if (store.updateVoucherBatch && data.batch) {
          store.updateVoucherBatch(data.batch.id, data.batch);
        }
        break;

      case 'BATCH_CREATE':
        if (store.createVoucherBatch && data.batch) {
          // Add batch to store
          store.createVoucherBatch(
            data.batch.department,
            data.batch.voucherIds,
            data.batch.sentBy
          );
        }
        break;

      case 'NOTIFICATION_CREATE':
        if (store.addNotification && data.notification) {
          store.addNotification(data.notification);
        }
        break;

      case 'USER_UPDATE':
        if (store.updateUser && data.user) {
          store.updateUser(data.user.id, data.user);
        }
        break;

      default:
        console.log('Unknown state update type:', data.type);
    }
  });

  // Handle user updates
  socket.on('user_update', (data) => {
    console.log('Received user update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        if (store.addUser && data.user) {
          store.addUser(data.user);
          // Force a reload to update the login dropdown
          window.location.reload();
        }
        break;

      case 'updated':
        if (store.updateUser && data.user) {
          store.updateUser(data.user.id, data.user);
        }
        break;

      case 'deleted':
        if (store.deleteUser && data.user) {
          store.deleteUser(data.user.id);
          // Force a reload to update the login dropdown
          window.location.reload();
        }
        break;

      case 'approved':
        if (store.addUser && data.user) {
          store.addUser(data.user);
          // Force a reload to update the login dropdown
          window.location.reload();
        }
        break;

      default:
        console.log('Unknown user update type:', data.type);
    }
  });

  // Handle registration updates
  socket.on('registration_update', (data) => {
    console.log('Received registration update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        if (data.registration) {
          console.log('New registration received:', data.registration);

          // Directly add the registration to the store
          const pendingRegistrations = store.pendingRegistrations || [];
          const exists = pendingRegistrations.some(reg => reg.id === data.registration.id);

          if (!exists) {
            store.setPendingRegistrations([...pendingRegistrations, data.registration]);
            console.log('Added new registration to store');
          }

          // Also fetch from server to ensure consistency
          store.fetchPendingRegistrations().then(() => {
            console.log('Fetched pending registrations after new registration');
          });
        }
        break;

      case 'approved':
        if (data.registration) {
          console.log('Registration approved:', data.registration);

          // Remove the registration from pending list
          const updatedRegistrations = (store.pendingRegistrations || []).filter(
            reg => reg.id !== data.registration.id
          );
          store.setPendingRegistrations(updatedRegistrations);

          // Also fetch from server to ensure consistency
          store.fetchPendingRegistrations().then(() => {
            console.log('Fetched pending registrations after approval');
          });
        }
        break;

      case 'rejected':
        if (data.registration) {
          console.log('Registration rejected:', data.registration);

          // Remove the registration from pending list
          const updatedRegistrations = (store.pendingRegistrations || []).filter(
            reg => reg.id !== data.registration.id
          );
          store.setPendingRegistrations(updatedRegistrations);

          // Also fetch from server to ensure consistency
          store.fetchPendingRegistrations().then(() => {
            console.log('Fetched pending registrations after rejection');
          });
        }
        break;

      default:
        console.log('Unknown registration update type:', data.type);
    }
  });

  // Handle voucher updates
  socket.on('voucher_update', (data) => {
    console.log('Received voucher update:', data);
    const store = useAppStore.getState();
    const currentUser = store.currentUser;

    // Track processed events to avoid duplicates
    const processedEvents = window._processedSocketEvents = window._processedSocketEvents || new Set();

    // Check if we've already processed this event (if it has an eventId)
    if (data.eventId && processedEvents.has(data.eventId)) {
      console.log(`Skipping duplicate event: ${data.eventId}`);
      return;
    }

    // Mark this event as processed if it has an eventId
    if (data.eventId) {
      processedEvents.add(data.eventId);

      // Clean up old events (keep only the last 100)
      if (processedEvents.size > 100) {
        const toRemove = Array.from(processedEvents).slice(0, processedEvents.size - 100);
        toRemove.forEach(id => processedEvents.delete(id));
      }
    }

    // Log current user info for debugging
    console.log('Current user:', currentUser ? `${currentUser.name} (${currentUser.department})` : 'Not logged in');

    // Skip processing if no current user
    if (!currentUser) {
      console.log('No current user, skipping voucher update');
      return;
    }

    // Check if this voucher belongs to the current user's department or if user is AUDIT/SYSTEM ADMIN
    const voucherDepartment = data.voucher?.department;
    const isRelevantForUser =
      currentUser.department === 'AUDIT' ||
      currentUser.department === 'SYSTEM ADMIN' ||
      voucherDepartment === currentUser.department;

    if (!isRelevantForUser) {
      console.log(`Voucher from ${voucherDepartment} department not relevant for ${currentUser.department} user, skipping`);
      return;
    }

    console.log(`Processing voucher update for ${voucherDepartment} department as ${currentUser.department} user`);

    switch (data.type) {
      case 'created':
        if (store.addVoucher && data.voucher) {
          // CRITICAL FIX: Check if the voucher already exists before adding it
          const existingVoucher = store.vouchers.find(v => v.id === data.voucher.id || v.voucherId === data.voucher.voucher_id);
          if (!existingVoucher) {
            console.log('Adding new voucher:', data.voucher.voucher_id);
            store.addVoucher(data.voucher);

            // Show toast notification for new voucher
            if (window.toast) {
              window.toast({
                title: "New Voucher",
                description: `Voucher ${data.voucher.voucher_id} has been created in ${data.voucher.department}`,
                variant: "default",
              });
            }

            // Force a refresh of vouchers from the server to ensure consistency
            store.fetchVouchers(currentUser?.department).then(() => {
              console.log('Refreshed vouchers after new voucher creation');
            });
          } else {
            console.log('Voucher already exists, not adding duplicate:', data.voucher.voucher_id);
          }
        }
        break;

      case 'updated':
        if (store.updateVoucher && data.voucher) {
          console.log('Updating voucher:', data.voucher.voucher_id);
          store.updateVoucher(data.voucher.id, data.voucher);

          // Show toast notification for updated voucher
          if (window.toast) {
            window.toast({
              title: "Voucher Updated",
              description: `Voucher ${data.voucher.voucher_id} has been updated`,
              variant: "default",
            });
          }

          // Force a refresh of vouchers from the server to ensure consistency
          store.fetchVouchers(currentUser?.department).then(() => {
            console.log('Refreshed vouchers after voucher update');
          });
        }
        break;

      case 'deleted':
        if (store.deleteVoucher && data.voucher) {
          console.log('Deleting voucher:', data.voucher.voucher_id || data.voucher.id);
          store.deleteVoucher(data.voucher.id);

          // Show toast notification for deleted voucher
          if (window.toast) {
            window.toast({
              title: "Voucher Deleted",
              description: `Voucher ${data.voucher.voucher_id || data.voucher.id} has been deleted`,
              variant: "default",
            });
          }

          // Force a refresh of vouchers from the server to ensure consistency
          store.fetchVouchers(currentUser?.department).then(() => {
            console.log('Refreshed vouchers after voucher deletion');
          });
        }
        break;

      default:
        console.log('Unknown voucher update type:', data.type);
    }
  });

  // Handle batch updates
  socket.on('batch_update', (data) => {
    console.log('Received batch update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        if (store.addBatch && data.batch) {
          store.addBatch(data.batch);
        }
        break;

      case 'updated':
        if (store.updateBatch && data.batch) {
          store.updateBatch(data.batch.id, data.batch);
        }
        break;

      case 'deleted':
        if (store.deleteBatch && data.batch) {
          store.deleteBatch(data.batch.id);
        }
        break;

      default:
        console.log('Unknown batch update type:', data.type);
    }
  });

  // Handle notification updates
  socket.on('notification_update', (data) => {
    console.log('Received notification update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        if (store.addNotification && data.notification) {
          store.addNotification(data.notification);
        }
        break;

      case 'updated':
        if (store.updateNotification && data.notification) {
          store.updateNotification(data.notification.id, data.notification);
        }
        break;

      case 'deleted':
        if (store.deleteNotification && data.notification) {
          store.deleteNotification(data.notification.id);
        }
        break;

      default:
        console.log('Unknown notification update type:', data.type);
    }
  });

  // Handle generic data updates
  socket.on('data_update', (data) => {
    console.log(`Received ${data.actionType} for ${data.entityType}:`, data.data);
    const store = useAppStore.getState();

    // Handle different entity types
    switch (data.entityType) {
      case 'voucher':
        handleVoucherUpdate(store, data.actionType, data.data);
        break;

      case 'batch':
        handleBatchUpdate(store, data.actionType, data.data);
        break;

      case 'user':
        handleUserUpdate(store, data.actionType, data.data);
        break;

      case 'notification':
        handleNotificationUpdate(store, data.actionType, data.data);
        break;

      case 'registration':
        handleRegistrationUpdate(store, data.actionType, data.data);
        break;

      default:
        console.log('Unknown entity type:', data.entityType);
    }
  });

  // Handle heartbeat acknowledgment
  socket.on('heartbeat_ack', (data) => {
    console.log('Heartbeat acknowledged:', data);
  });

  // Handle resource viewers updates
  socket.on('resource_viewers', (data) => {
    console.log('Received resource viewers update:', data);
    const store = useAppStore.getState();

    if (store.updateResourceViewers) {
      store.updateResourceViewers(data.resourceKey, data.viewers, data.viewerCount);
    }
  });

  // Handle force refresh
  socket.on('force_refresh', (data) => {
    console.log('Received force refresh:', data);
    const store = useAppStore.getState();
    const currentUser = store.currentUser;

    if (!currentUser) {
      console.log('No current user, skipping force refresh');
      return;
    }

    if (data.type === 'vouchers') {
      console.log('Force refreshing vouchers for department:', data.department);
      store.fetchVouchers(data.department).then(() => {
        console.log('Completed force refresh of vouchers');
      });
    }
  });

  // Handle user joined
  socket.on('user_joined', (data) => {
    console.log('User joined:', data);

    // Show toast notification
    if (window.toast) {
      window.toast({
        title: "User Joined",
        description: `${data.userName} has joined the ${data.department} department`,
        variant: "default",
      });
    }

    // Force refresh users to update active users display
    const store = useAppStore.getState();
    store.fetchAllUsers();

    // Force refresh vouchers to ensure we have the latest data
    const currentUser = store.currentUser;
    if (currentUser && currentUser.department === data.department) {
      store.fetchVouchers(currentUser.department);
    }
  });

  // Handle user left
  socket.on('user_left', (data) => {
    console.log('User left:', data);

    // Show toast notification
    if (window.toast) {
      window.toast({
        title: "User Left",
        description: `${data.userName} has left the ${data.department} department`,
        variant: "default",
      });
    }

    // Force refresh users to update active users display
    const store = useAppStore.getState();
    store.fetchAllUsers();

    // Force refresh vouchers to ensure we have the latest data
    const currentUser = store.currentUser;
    if (currentUser && currentUser.department === data.department) {
      store.fetchVouchers(currentUser.department);
    }
  });
};

// Helper functions for data updates
function handleVoucherUpdate(store: any, actionType: string, data: any) {
  const currentUser = store.currentUser;

  // Log current user info for debugging
  console.log('Current user in data_update:', currentUser ? `${currentUser.name} (${currentUser.department})` : 'Not logged in');

  // Skip processing if no current user
  if (!currentUser) {
    console.log('No current user, skipping voucher data_update');
    return;
  }

  // Check if this voucher belongs to the current user's department or if user is AUDIT/SYSTEM ADMIN
  const voucherDepartment = data.department;
  const isRelevantForUser =
    currentUser.department === 'AUDIT' ||
    currentUser.department === 'SYSTEM ADMIN' ||
    voucherDepartment === currentUser.department;

  if (!isRelevantForUser) {
    console.log(`Voucher from ${voucherDepartment} department not relevant for ${currentUser.department} user, skipping data_update`);
    return;
  }

  console.log(`Processing voucher data_update for ${voucherDepartment} department as ${currentUser.department} user`);

  switch (actionType) {
    case 'created':
      if (store.addVoucher) {
        // CRITICAL FIX: Check if the voucher already exists before adding it
        const existingVoucher = store.vouchers.find(v => v.id === data.id || v.voucherId === data.voucher_id);
        if (!existingVoucher) {
          console.log('Adding new voucher from data_update:', data.voucher_id || data.id);
          store.addVoucher(data);

          // Show toast notification for new voucher
          if (window.toast) {
            window.toast({
              title: "New Voucher",
              description: `Voucher ${data.voucher_id || data.id} has been created in ${data.department}`,
              variant: "default",
            });
          }

          // Force a refresh of vouchers from the server to ensure consistency
          store.fetchVouchers(currentUser?.department).then(() => {
            console.log('Refreshed vouchers after new voucher creation from data_update');
          });
        } else {
          console.log('Voucher already exists, not adding duplicate from data_update:', data.voucher_id || data.id);
        }
      }
      break;

    case 'updated':
      if (store.updateVoucher) {
        console.log('Updating voucher from data_update:', data.voucher_id || data.id);
        store.updateVoucher(data.id, data);

        // Show toast notification for updated voucher
        if (window.toast) {
          window.toast({
            title: "Voucher Updated",
            description: `Voucher ${data.voucher_id || data.id} has been updated`,
            variant: "default",
          });
        }

        // Force a refresh of vouchers from the server to ensure consistency
        store.fetchVouchers(currentUser?.department).then(() => {
          console.log('Refreshed vouchers after voucher update from data_update');
        });
      }
      break;

    case 'deleted':
      if (store.deleteVoucher) {
        console.log('Deleting voucher from data_update:', data.voucher_id || data.id);
        store.deleteVoucher(data.id);

        // Show toast notification for deleted voucher
        if (window.toast) {
          window.toast({
            title: "Voucher Deleted",
            description: `Voucher ${data.voucher_id || data.id} has been deleted`,
            variant: "default",
          });
        }

        // Force a refresh of vouchers from the server to ensure consistency
        store.fetchVouchers(currentUser?.department).then(() => {
          console.log('Refreshed vouchers after voucher deletion from data_update');
        });
      }
      break;
  }
}

function handleBatchUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.addBatch) {
        store.addBatch(data);
      }
      break;

    case 'updated':
      if (store.updateBatch) {
        store.updateBatch(data.id, data);
      }
      break;

    case 'deleted':
      if (store.deleteBatch) {
        store.deleteBatch(data.id);
      }
      break;
  }
}

function handleUserUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.addUser) {
        store.addUser(data);
      }
      break;

    case 'updated':
      if (store.updateUser) {
        store.updateUser(data.id, data);
      }
      break;

    case 'deleted':
      if (store.deleteUser) {
        store.deleteUser(data.id);
      }
      break;
  }
}

function handleNotificationUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.addNotification) {
        store.addNotification(data);
      }
      break;

    case 'updated':
      if (store.updateNotification) {
        store.updateNotification(data.id, data);
      }
      break;

    case 'deleted':
      if (store.deleteNotification) {
        store.deleteNotification(data.id);
      }
      break;
  }
}

function handleRegistrationUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.pendingRegistrations) {
        const pendingRegistrations = store.pendingRegistrations || [];
        const exists = pendingRegistrations.some(reg => reg.id === data.id);

        if (!exists && store.setPendingRegistrations) {
          store.setPendingRegistrations([...pendingRegistrations, data]);
        }
      }
      break;

    case 'approved':
    case 'rejected':
      if (store.pendingRegistrations && store.setPendingRegistrations) {
        const updatedRegistrations = (store.pendingRegistrations || []).filter(
          reg => reg.id !== data.id
        );
        store.setPendingRegistrations(updatedRegistrations);
      }
      break;
  }
}

// Track resource viewing and editing
export const viewResource = (resourceType: string, resourceId: string, isViewing: boolean, targetDepartment?: string): Promise<any> => {
  return new Promise((resolve) => {
    if (!socket) {
      resolve({ success: false, message: 'Socket not connected' });
      return;
    }

    const currentUser = useAppStore.getState().currentUser;
    const timestamp = Date.now();

    // Include targetDepartment if provided (for Audit department-specific resources)
    socket.emit('view_resource', { 
      resourceType, 
      resourceId, 
      targetDepartment, 
      isViewing,
      userId: currentUser?.id,
      userName: currentUser?.name,
      timestamp,
      action: isViewing ? 'EDITING' : 'OPENED'
    }, (response: any) => {
      resolve(response);
    });
  });
};

// Add inactivity timer for auto-release
export const startInactivityTimer = (resourceType: string, resourceId: string, targetDepartment?: string) => {
  // Clear any existing timer
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }

  // Set new timer
  inactivityTimer = setTimeout(() => {
    // Auto-release the lock after inactivity
    releaseLock(resourceType, resourceId, targetDepartment).then(() => {
      if (window.toast) {
        window.toast({
          title: "Lock Released",
          description: "Your editing access has been released due to inactivity",
          variant: "default",
        });
      }
      
      // Update the resource state to OPENED
      viewResource(resourceType, resourceId, false, targetDepartment);
    });
  }, INACTIVITY_TIMEOUT);
};

export const resetInactivityTimer = (resourceType: string, resourceId: string, targetDepartment?: string) => {
  // Reset the timer when there's activity
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }
  startInactivityTimer(resourceType, resourceId, targetDepartment);
};

// Update the requestLock function to handle the EDITING state
export const requestLock = (resourceType: string, resourceId: string, targetDepartment?: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!socket) {
      resolve(false);
      return;
    }

    const currentUser = useAppStore.getState().currentUser;

    socket.emit('lock_request', { 
      resourceType, 
      resourceId, 
      targetDepartment,
      userId: currentUser?.id,
      userName: currentUser?.name,
      action: 'EDITING'
    }, (response: any) => {
      if (response.success) {
        // Start inactivity timer when lock is acquired
        startInactivityTimer(resourceType, resourceId, targetDepartment);
        
        // Update the resource state to EDITING
        viewResource(resourceType, resourceId, true, targetDepartment);
      }
      resolve(response.success);
    });
  });
};

// Update the releaseLock function
export const releaseLock = (resourceType: string, resourceId: string, targetDepartment?: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!socket) {
      resolve(false);
      return;
    }

    // Clear inactivity timer
    if (inactivityTimer) {
      clearTimeout(inactivityTimer);
      inactivityTimer = null;
    }

    const currentUser = useAppStore.getState().currentUser;

    socket.emit('lock_release', { 
      resourceType, 
      resourceId, 
      targetDepartment,
      userId: currentUser?.id,
      userName: currentUser?.name,
      action: 'OPENED'
    }, (response: any) => {
      if (response.success) {
        // Update the resource state to OPENED
        viewResource(resourceType, resourceId, false, targetDepartment);
      }
      resolve(response.success);
    });
  });
};

// Update the sendActivity function to reset the inactivity timer
export const sendActivity = (resourceType: string, resourceId: string, targetDepartment?: string): Promise<any> => {
  return new Promise((resolve) => {
    if (!socket) {
      resolve({ success: false, message: 'Socket not connected' });
      return;
    }

    // Reset inactivity timer
    resetInactivityTimer(resourceType, resourceId, targetDepartment);

    const currentUser = useAppStore.getState().currentUser;

    socket.emit('activity', { 
      resourceType, 
      resourceId, 
      targetDepartment,
      userId: currentUser?.id,
      userName: currentUser?.name,
      timestamp: Date.now()
    }, (response: any) => {
      resolve(response);
    });
  });
};

// Send state update to other clients
export const sendStateUpdate = (updateData: any): void => {
  if (!socket) {
    return;
  }

  socket.emit('state_update', updateData);
};

// Start heartbeat to keep connection alive
const startHeartbeat = (): void => {
  if (!socket) {
    return;
  }

  // Send heartbeat every 30 seconds
  const heartbeatInterval = setInterval(() => {
    if (!socket) {
      clearInterval(heartbeatInterval);
      return;
    }

    socket.emit('heartbeat');
  }, HEARTBEAT_INTERVAL);

  // Clear interval on disconnect
  socket.on('disconnect', () => {
    clearInterval(heartbeatInterval);
  });
};

// Stop heartbeat
const stopHeartbeat = (): void => {
  if (!socket) {
    return;
  }

  // Clear all intervals
  socket.removeAllListeners('disconnect');
};

// Export socket service
export default {
  connectSocket,
  disconnectSocket,
  getSocket,
  requestLock,
  releaseLock,
  sendStateUpdate,
  viewResource,
  sendActivity,
};
