import { StateCreator } from 'zustand';
import { AppState } from '../store';
import { formatCurrentDate } from '../utils';
import { SystemSettings } from '../types';

export interface AdminSlice {
  systemSettings: SystemSettings;
  lastBackupDate: string | null;
  isPerformingBackup: boolean;
  isRestoringBackup: boolean;
  backupError: string | null;
  restoreError: string | null;
  configureSystem: (settings: Partial<SystemSettings>) => void;
  backupSystemData: () => void;
  restoreSystemData: (file: File) => void;
  clearLocalStorage: () => void;
}

export const createAdminSlice: StateCreator<AppState, [], [], AdminSlice> = (set, get) => ({
  systemSettings: {
    fiscalYearStart: 'JAN',
    fiscalYearEnd: 'DEC',
    currentFiscalYear: new Date().getFullYear(),
    systemTime: new Date().toISOString(),
    sessionTimeout: 30,
    autoBackupEnabled: true
  },
  lastBackupDate: null,
  isPerformingBackup: false,
  isRestoringBackup: false,
  backupError: null,
  restoreError: null,

  configureSystem: (settings) => {
    set((state) => ({
      systemSettings: {
        ...state.systemSettings,
        ...settings
      }
    }));
  },

  backupSystemData: () => {
    set({ isPerformingBackup: true, backupError: null });
    try {
      // Get current date in ISO format for consistent storage
      const currentDate = new Date().toISOString();

      // Update the state with the new backup date first
      set({ lastBackupDate: currentDate });

      // Then get the updated state for the backup file
      const state = get();
      const backupData = {
        ...state,
        lastBackupDate: currentDate
      };

      // Create and download the backup file
      const blob = new Blob([JSON.stringify(backupData)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `voucher_system_backup_${formatCurrentDate()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Update the state again to ensure the backup date is saved
      set({ isPerformingBackup: false });

      // Force a save to localStorage to ensure persistence
      localStorage.setItem('voucher-management-system', JSON.stringify({
        state: { ...get(), lastBackupDate: currentDate }
      }));

      return true;
    } catch (error) {
      console.error('Backup error:', error);
      set({
        backupError: error instanceof Error ? error.message : 'Backup failed',
        isPerformingBackup: false
      });
      return false;
    }
  },

  restoreSystemData: (file) => {
    set({ isRestoringBackup: true, restoreError: null });
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const backupData = JSON.parse(e.target?.result as string);

        // Save to localStorage with the correct key for our persist middleware
        localStorage.setItem('voucher-management-system', JSON.stringify({
          state: backupData
        }));

        // Update the current state
        set({
          ...backupData,
          isRestoringBackup: false
        });

        // Reload the page to apply changes
        setTimeout(() => {
          window.location.reload();
        }, 1000);

        return true;
      } catch (error) {
        set({
          restoreError: error instanceof Error ? error.message : 'Restore failed',
          isRestoringBackup: false
        });
        return false;
      }
    };
    reader.readAsText(file);
  },

  clearLocalStorage: () => {
    try {
      localStorage.clear();
      window.location.reload();
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }
});
