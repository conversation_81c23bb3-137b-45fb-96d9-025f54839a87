import { StateCreator } from 'zustand';
import { TransactionStatus, Voucher, Department } from '../../types';
import { formatCurrentDate } from '../utils';

export interface AppState {
  currentUser: { name: string } | null;
  vouchers: Voucher[];
  voucherBatches: Array<{ id: string }>;
  users: Array<{ id: string; department: Department }>;
  createVoucherBatch: (department: Department, voucherIds: string[], dispatchedBy: string) => void;
  updateVoucher: (id: string, data: Partial<Voucher>) => void;
  addNotification: (data: { userId: string; message: string; voucherId: string; type: string; batchId: string; fromAudit: boolean }) => void;
}

export interface AuditSlice {
  sendVouchersToAudit: (department: Department, voucherIds: string[], dispatchedBy: string) => void;
  sendVouchersFromAuditToDepartment: (department: Department, voucherIds: string[]) => void;
}

export const createAuditSlice: StateCreator<AppState, [], [], AuditSlice> = (set, get) => ({
  sendVouchersToAudit: (department: Department, voucherIds: string[], dispatchedBy: string) => {
    if (voucherIds.length === 0) return;

    // Check if any vouchers are already sent to audit
    const alreadySentVouchers = get().vouchers.filter(
      (v: Voucher) => voucherIds.includes(v.id) && v.sentToAudit
    );

    if (alreadySentVouchers.length > 0) {
      throw new Error("Some vouchers have already been sent to Audit");
    }

    // Create a new batch
    get().createVoucherBatch(department, voucherIds, dispatchedBy);
  },

  sendVouchersFromAuditToDepartment: (department: Department, voucherIds: string[]) => {
    if (voucherIds.length === 0) return;

    const currentUser = get().currentUser;
    if (!currentUser) return;

    console.log(`Sending ${voucherIds.length} vouchers from Audit to ${department} department`);

    // Create a batch for vouchers being sent FROM Audit TO department
    const newBatch = {
      id: `batch${Date.now()}`,
      department,
      voucherIds,
      sentBy: currentUser.name,
      sentTime: formatCurrentDate(),
      received: false,
      fromAudit: true
    };

    // Add the batch to store
    set((state: AppState) => ({
      voucherBatches: [...state.voucherBatches, newBatch]
    }));

    // Update vouchers to be sent back to department
    voucherIds.forEach((id: string) => {
      // Get current voucher data
      const voucher = get().vouchers.find((v: Voucher) => v.id === id);
      if (voucher) {
        // Check if this is a returned voucher
        const isVoucherReturned = voucher.pendingReturn || voucher.isReturned || false;

        console.log(`Sending voucher ${voucher.id} from Audit to ${department}, isReturned: ${isVoucherReturned}, pendingReturn: ${voucher.pendingReturn}, current status: ${voucher.status}`);

        // For returned vouchers, ensure we preserve both returnComment and comment
        const updateData: Partial<Voucher> = {
          status: isVoucherReturned ? "VOUCHER RETURNED" as TransactionStatus : "VOUCHER CERTIFIED" as TransactionStatus,
          auditDispatchedBy: voucher.auditDispatchedBy || currentUser.name,
          auditDispatchTime: formatCurrentDate(),
          isReturned: isVoucherReturned,
          pendingReturn: false, // Clear pendingReturn flag
          dispatched: true,
          batchId: newBatch.id
        };

        // For returned vouchers, ensure we preserve the comments
        if (isVoucherReturned) {
          const existingReturnComment = voucher.returnComment || voucher.comment;
          const existingComment = voucher.comment || voucher.returnComment;

          // Ensure we have a valid comment
          const finalComment = String(existingReturnComment || existingComment || "NO COMMENT PROVIDED").trim();

          console.log(`Preserving return comment for voucher ${voucher.id}: "${finalComment}"`);

          updateData.returnComment = finalComment;
          updateData.comment = finalComment;
        }

        // Update the voucher in the store
        console.log(`Final update data for voucher ${voucher.id} before sending to store:`, {
          status: updateData.status,
          isReturned: updateData.isReturned,
          pendingReturn: updateData.pendingReturn,
          dispatched: updateData.dispatched,
          returnComment: updateData.returnComment?.substring(0, 20) + (updateData.returnComment && updateData.returnComment.length > 20 ? '...' : '')
        });

        get().updateVoucher(id, updateData);

        // Verify the update was applied
        setTimeout(() => {
          const updatedVoucher = get().vouchers.find(v => v.id === id);
          if (updatedVoucher) {
            console.log(`After sending to department, voucher ${voucher.id} state:`, {
              status: updatedVoucher.status,
              isReturned: updatedVoucher.isReturned,
              pendingReturn: updatedVoucher.pendingReturn,
              dispatched: updatedVoucher.dispatched
            });
          }
        }, 100);
      }
    });

    // Notify department user about each voucher
    const departmentUser = get().users.find(u => u.department === department);
    if (departmentUser) {
      voucherIds.forEach(id => {
        const voucher = get().vouchers.find(v => v.id === id);
        if (voucher) {
          // Determine notification type based on voucher status
          const notificationType = voucher.status === "VOUCHER REJECTED"
            ? "VOUCHER_REJECTED"
            : voucher.isReturned || voucher.pendingReturn
              ? "VOUCHER_RETURNED"
              : "VOUCHER_CERTIFIED";

          // Add notification to the department user
          get().addNotification({
            userId: departmentUser.id,
            message: `VOUCHER ${voucher.voucherId} SENT BACK FROM AUDIT`,
            voucherId: id,
            type: notificationType,
            batchId: newBatch.id,
            fromAudit: true
          });
        }
      });
    }

    console.log(`Successfully sent ${voucherIds.length} vouchers to ${department}, created batch ${newBatch.id}`);
  },
});
