import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { authApi, usersApi } from '@/lib/api';
import { connectSocket, disconnectSocket } from '@/lib/socket';

export interface AuthSlice {
  currentUser: AppState['currentUser'];
  login: AppState['login'];
  logout: AppState['logout'];
  registerUser: AppState['registerUser'];
  pendingRegistrations: AppState['pendingRegistrations'];
  approvePendingUser: AppState['approvePendingUser'];
  rejectPendingUser: AppState['rejectPendingUser'];
  setMockAdminUser: AppState['setMockAdminUser'];
  fetchCurrentUser: () => Promise<boolean>;
  fetchPendingRegistrations: () => Promise<boolean>;
}

export const createAuthSlice: StateCreator<AppState, [], [], AuthSlice> = (set, get) => ({
  currentUser: null,
  pendingRegistrations: [],
  login: async (department, username, password, isGuest = false) => {
    try {
      // For development/testing, use the local login if isGuest is true
      if (isGuest) {
        // Normalize inputs for case-insensitive comparison
        const normalizedDepartment = department.toUpperCase();
        const normalizedUsername = username.toUpperCase();

        console.log('Guest login attempt with:', {
          department: normalizedDepartment,
          username: normalizedUsername
        });

        // Find the user with case-insensitive matching
        const user = get().users.find(u => {
          const userDept = (u.department || '').toUpperCase();
          const userName = (u.name || '').toUpperCase();
          return userDept === normalizedDepartment && userName === normalizedUsername;
        });

        if (user && user.password === password) {
          // Update user with last login time
          const updatedUser = {
            ...user,
            lastLogin: new Date().toISOString()
          };

          // Update the user in the store
          set({ currentUser: updatedUser });

          // Also update the user in the users array
          get().updateUser(user.id, { lastLogin: updatedUser.lastLogin });

          console.log('Guest login successful for user:', updatedUser);
          return true;
        }

        return false;
      }

      // Call login API for production use
      const response = await authApi.login(department, username, password);

      // Save token to localStorage
      localStorage.setItem('auth_token', response.token);

      // Set current user in state
      set({ currentUser: response.user });

      // Connect to WebSocket
      connectSocket(response.token);

      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  },
  logout: async () => {
    try {
      // CRITICAL FIX: Improved logout with session termination
      console.log('Logging out user:', get().currentUser?.name);

      // Call logout API to terminate the session on the server
      await authApi.logout();
      console.log('Server-side logout successful');

      // Disconnect from WebSocket
      disconnectSocket();
      console.log('WebSocket disconnected');

      // Clear token from localStorage
      localStorage.removeItem('auth_token');
      console.log('Auth token removed from localStorage');

      // Clear current user in state
      set({ currentUser: null });
      console.log('User state cleared');

      return true;
    } catch (error) {
      console.error('Logout error:', error);

      // Even if API call fails, clear local state
      localStorage.removeItem('auth_token');
      disconnectSocket();
      set({ currentUser: null });
      console.log('Forced logout due to error');

      return true;
    }
  },

  registerUser: async (name, password, department) => {
    try {
      // Call register API
      await authApi.register(name, password, department);

      // For local state, create a pending registration
      const newRegistration = {
        id: `reg-${Date.now()}`,
        name: name.toUpperCase(),
        password,
        department: department.toUpperCase(),
        dateRequested: new Date().toISOString(),
        status: 'pending'
      };

      set(state => ({
        pendingRegistrations: [...state.pendingRegistrations, newRegistration]
      }));

      return true;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    }
  },

  approvePendingUser: async (registrationId) => {
    try {
      // Call approve registration API
      await usersApi.approveRegistration(registrationId);

      // Update local state
      const registration = get().pendingRegistrations.find(r => r.id === registrationId);

      if (registration) {
        // Create a new user from the registration for local state
        const newUser = {
          id: `user-${Date.now()}`,
          name: registration.name.toUpperCase(),
          password: registration.password,
          department: registration.department.toUpperCase(),
          role: 'VIEWER',
          dateCreated: new Date().toISOString(),
          isActive: true
        };

        // Add the user to the users list
        get().addUser(newUser);
      }

      // Remove from pending registrations
      set(state => ({
        pendingRegistrations: state.pendingRegistrations.filter(r => r.id !== registrationId)
      }));

      return true;
    } catch (error) {
      console.error('Approve registration error:', error);
      return false;
    }
  },

  rejectPendingUser: async (registrationId) => {
    try {
      // Call reject registration API
      await usersApi.rejectRegistration(registrationId);

      // Update local state
      set(state => ({
        pendingRegistrations: state.pendingRegistrations.filter(r => r.id !== registrationId)
      }));

      return true;
    } catch (error) {
      console.error('Reject registration error:', error);
      return false;
    }
  },

  setMockAdminUser: (user) => {
    set({ currentUser: user });
  },

  // Fetch current user from API
  fetchCurrentUser: async () => {
    try {
      // Check if token exists
      const token = localStorage.getItem('auth_token');
      if (!token) {
        return false;
      }

      // Call get current user API
      const user = await authApi.getCurrentUser();

      // Set current user in state
      set({ currentUser: user });

      // Connect to WebSocket
      connectSocket(token);

      return true;
    } catch (error) {
      console.error('Fetch current user error:', error);

      // Clear token and user if error
      localStorage.removeItem('auth_token');
      set({ currentUser: null });

      return false;
    }
  },

  // Directly set pending registrations in state
  setPendingRegistrations: (registrations) => {
    set({ pendingRegistrations: registrations });
    console.log('Set pending registrations:', registrations.length);
  },

  // Fetch pending registrations from API
  fetchPendingRegistrations: async () => {
    try {
      // Call get pending registrations API with timestamp to prevent caching
      const registrations = await usersApi.getPendingRegistrations();

      // Set pending registrations in state
      set({ pendingRegistrations: registrations });

      console.log('Fetched pending registrations:', registrations.length);

      return true;
    } catch (error) {
      console.error('Fetch pending registrations error:', error);
      return false;
    }
  },
});
