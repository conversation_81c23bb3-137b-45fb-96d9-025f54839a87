
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { initialNotifications } from '../../data';
import { formatCurrentDate } from '../utils';

export interface NotificationsSlice {
  notifications: AppState['notifications'];
  addNotification: AppState['addNotification'];
  markNotificationAsRead: AppState['markNotificationAsRead'];
  deleteNotification: AppState['deleteNotification'];
}

export const createNotificationsSlice: StateCreator<AppState, [], [], NotificationsSlice> = (set) => ({
  notifications: initialNotifications,
  addNotification: (notification) => {
    const newNotification = {
      ...notification,
      id: `n${Date.now()}`,
      timestamp: formatCurrentDate(),
      isRead: false,
      fromAudit: notification.fromAudit || false
    };
    
    set((state) => ({ 
      notifications: [...state.notifications, newNotification] 
    }));
    
    return newNotification;
  },
  markNotificationAsRead: (notificationId) => set((state) => ({
    notifications: state.notifications.map(notification => 
      notification.id === notificationId ? { ...notification, isRead: true } : notification
    )
  })),
  deleteNotification: (notificationId) => set((state) => ({
    notifications: state.notifications.filter(notification => notification.id !== notificationId)
  })),
});
