
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { initialProvisionalCashRecords } from '../../data';

export interface ProvisionalCashRecordsSlice {
  provisionalCashRecords: AppState['provisionalCashRecords'];
  addProvisionalCashRecord: AppState['addProvisionalCashRecord'];
  updateProvisionalCashRecord: AppState['updateProvisionalCashRecord'];
  deleteProvisionalCashRecord: AppState['deleteProvisionalCashRecord'];
}

export const createProvisionalCashRecordsSlice: StateCreator<AppState, [], [], ProvisionalCashRecordsSlice> = (set) => ({
  provisionalCashRecords: initialProvisionalCashRecords,
  addProvisionalCashRecord: (record) => {
    const newRecord = {
      ...record,
      id: `pcr${Date.now()}`
    };
    
    set((state) => ({ 
      provisionalCashRecords: [...state.provisionalCashRecords, newRecord] 
    }));
    
    return newRecord;
  },
  updateProvisionalCashRecord: (recordId, recordData) => set((state) => ({
    provisionalCashRecords: state.provisionalCashRecords.map(record => {
      if (record.id === recordId) {
        // If amountRetired is undefined or null, also clear related fields
        if (recordData.amountRetired === undefined || recordData.amountRetired === null) {
          return {
            ...record,
            ...recordData,
            amountRetired: undefined,
            clearanceRemark: undefined,
            dateRetired: undefined,
            clearedBy: undefined
          };
        }
        return { ...record, ...recordData };
      }
      return record;
    })
  })),
  deleteProvisionalCashRecord: (recordId) => set((state) => ({
    provisionalCashRecords: state.provisionalCashRecords.filter(record => record.id !== recordId)
  })),
});
