import { StateCreator } from 'zustand';
import { AppState } from '../types';

export interface ResourceViewersState {
  resourceViewers: Record<string, {
    viewers: Array<{userId: string, userName: string, department: string}>,
    viewerCount: number
  }>;
  updateResourceViewers: (resourceKey: string, viewers: Array<{userId: string, userName: string, department: string}>, viewerCount: number) => void;
  getResourceViewers: (resourceKey: string) => Array<{userId: string, userName: string, department: string}>;
  getResourceViewerCount: (resourceKey: string) => number;
}

export type ResourceViewersSlice = ResourceViewersState;

export const createResourceViewersSlice: StateCreator<
  AppState,
  [],
  [],
  ResourceViewersSlice
> = (set, get) => ({
  resourceViewers: {},
  
  updateResourceViewers: (resourceKey, viewers, viewerCount) => {
    set((state) => ({
      resourceViewers: {
        ...state.resourceViewers,
        [resourceKey]: {
          viewers,
          viewerCount
        }
      }
    }));
  },
  
  getResourceViewers: (resourceKey) => {
    return get().resourceViewers[resourceKey]?.viewers || [];
  },
  
  getResourceViewerCount: (resourceKey) => {
    return get().resourceViewers[resourceKey]?.viewerCount || 0;
  }
});
