
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { formatCurrentDate } from '../utils';

export interface VoucherBatchesSlice {
  voucherBatches: AppState['voucherBatches'];
  createVoucherBatch: AppState['createVoucherBatch'];
  receiveVoucherBatch: AppState['receiveVoucherBatch'];
}

export const createVoucherBatchesSlice: StateCreator<AppState, [], [], VoucherBatchesSlice> = (set, get) => ({
  voucherBatches: [],
  createVoucherBatch: (department, voucherIds, dispatchedBy) => {
    const currentUser = get().currentUser;
    if (!currentUser) throw new Error("No user logged in");

    const newBatch = {
      id: `batch${Date.now()}`,
      department,
      voucherIds,
      sentBy: currentUser.name,
      sentTime: formatCurrentDate(),
      received: false,
      fromAudit: false
    };

    // Update voucher status and sentToAudit flag
    const currentTime = formatCurrentDate();
    voucherIds.forEach(id => {
      console.log(`Setting dispatch info for voucher ${id}:`, {
        dispatchToAuditBy: dispatchedBy,
        dispatchedBy: dispatchedBy,
        dispatchTime: currentTime
      });

      get().updateVoucher(id, {
        status: "PENDING RECEIPT",
        sentToAudit: true,
        batchId: newBatch.id,
        dispatchToAuditBy: dispatchedBy,
        dispatchedBy: dispatchedBy,  // Set dispatchedBy field
        dispatchTime: currentTime,    // Set dispatchTime field
        // Ensure these fields are cleared to prevent issues with filtering
        auditDispatchTime: undefined,
        auditDispatchedBy: undefined,
        dispatched: false,
        certified: false,
        rejected: false,
        isReturned: false,
        pendingReturn: false
      });

      // Verify the update was applied
      setTimeout(() => {
        const updatedVoucher = get().vouchers.find(v => v.id === id);
        if (updatedVoucher) {
          console.log(`After dispatch to Audit, voucher ${id} dispatch info:`, {
            dispatchToAuditBy: updatedVoucher.dispatchToAuditBy,
            dispatchedBy: updatedVoucher.dispatchedBy,
            dispatchTime: updatedVoucher.dispatchTime
          });
        }
      }, 100);
    });

    // Add batch to store
    set((state) => ({
      voucherBatches: [...state.voucherBatches, newBatch]
    }));

    // Notify Audit department
    const auditUser = get().users.find(u => u.department === "AUDIT");
    if (auditUser) {
      get().addNotification({
        userId: auditUser.id,
        message: `NEW VOUCHER BATCH RECEIVED FROM ${department} DEPARTMENT (${voucherIds.length} VOUCHERS)`,
        batchId: newBatch.id,
        type: "NEW_BATCH",
        fromAudit: false
      });
    }

    return newBatch;
  },
  receiveVoucherBatch: (batchId, receivedVoucherIds, rejectedVoucherIds, rejectionComments = {}) => {
    console.log(`Receiving batch ${batchId} - Accepted: ${receivedVoucherIds.length}, Rejected: ${rejectedVoucherIds.length}`);

    const batch = get().voucherBatches.find(b => b.id === batchId);
    if (!batch) throw new Error("Batch not found");

    // Mark batch as received
    set((state) => ({
      voucherBatches: state.voucherBatches.map(batch =>
        batch.id === batchId ? { ...batch, received: true } : batch
      )
    }));

    const currentUser = get().currentUser;
    const currentTime = formatCurrentDate();

    // Handle received vouchers
    receivedVoucherIds.forEach(id => {
      // Get the current voucher to check if it's from a department to audit
      const voucher = get().vouchers.find(v => v.id === id);
      if (!voucher) return;

      // If this is a voucher being received by audit, set status to AUDIT: PROCESSING
      // If this is a voucher being received by a department from audit, set status to VOUCHER PROCESSING
      const newStatus = batch.fromAudit ? "VOUCHER PROCESSING" : "AUDIT: PROCESSING";

      console.log(`Receiving voucher ${id} with new status: ${newStatus}`);

      get().updateVoucher(id, {
        status: newStatus,
        receivedBy: currentUser?.name,
        receiptTime: currentTime
      });
    });

    // Handle rejected vouchers
    rejectedVoucherIds.forEach(id => {
      const voucher = get().vouchers.find(v => v.id === id);
      if (voucher) {
        const rejectionComment = rejectionComments[id] || "Rejected during batch receiving";
        console.log(`Rejecting voucher ${voucher.voucherId} (${id}) during batch receiving with comment: ${rejectionComment}`);

        // Update the voucher status to VOUCHER REJECTED with critical fields explicitly set
        get().updateVoucher(id, {
          status: "VOUCHER REJECTED",
          rejectionTime: currentTime,
          rejectedBy: currentUser?.name || "AUDIT USER",
          comment: rejectionComment,
          deleted: false, // Explicitly mark as not deleted
          sentToAudit: true // Keep this flag to ensure it appears in filters
        });

        // Add to blacklist
        get().addBlacklistedVoucherId(voucher.voucherId);

        // Notify department
        const departmentUser = get().users.find(u => u.department === voucher.department);
        if (departmentUser) {
          get().addNotification({
            userId: departmentUser.id,
            message: `VOUCHER ${voucher.voucherId} REJECTED BY AUDIT: ${rejectionComment}`,
            voucherId: id,
            type: "VOUCHER_REJECTED"
          });
        }
      }
    });
  },
});
