import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { initialVouchers, generateVoucherId } from '../../data';
import { formatCurrentDate } from '../utils';
import { TransactionStatus, Voucher } from '../../types';
import { vouchersApi } from '@/lib/api';
import { sendStateUpdate } from '@/lib/socket';

export interface VouchersSlice {
  vouchers: AppState['vouchers'];
  addVoucher: AppState['addVoucher'];
  updateVoucher: AppState['updateVoucher'];
  deleteVoucher: AppState['deleteVoucher'];
  fetchVouchers: (department?: string) => Promise<boolean>;
  sendVoucherToAudit: (voucherId: string) => Promise<boolean>;
  returnVoucher: (voucherId: string, returnComment: string) => Promise<boolean>;
  certifyVoucher: (voucherId: string) => Promise<boolean>;
  rejectVoucher: (voucherId: string, comment: string) => Promise<boolean>;
}

export const createVouchersSlice: StateCreator<AppState, [], [], VouchersSlice> = (set, get) => ({
  vouchers: [],
  addVoucher: async (voucherData) => {
    try {
      console.log('Creating new voucher with data:', voucherData);
      // For development/testing, use the local approach
      if (process.env.NODE_ENV === 'development' && !localStorage.getItem('auth_token')) {
        // Create a new voucher with required fields
        const newVoucher: Voucher = {
          id: `v${Date.now()}`,
          voucherId: generateVoucherId(voucherData.department), // Generate a voucher ID based on department
          date: voucherData.date || formatCurrentDate(),
          claimant: voucherData.claimant,
          description: voucherData.description,
          amount: voucherData.amount,
          currency: voucherData.currency,
          department: voucherData.department,
          dispatchedBy: "", // Make dispatchedBy blank by default
          dispatchTime: "", // Make dispatchTime blank by default
          status: "PENDING SUBMISSION" as TransactionStatus, // Set default status
          sentToAudit: false, // Set default sentToAudit value
          createdBy: get().currentUser?.name || "Unknown", // Set the creator to current user

          // Process any comments to ensure they're strings
          ...(voucherData.comment ? { comment: String(voucherData.comment) } : {}),
          ...(voucherData.returnComment ? { returnComment: String(voucherData.returnComment) } : {}),

          // Include any other properties from voucherData
          ...voucherData
        };

        console.log(`Adding new voucher: ${newVoucher.id}`);
        set((state) => ({ vouchers: [...state.vouchers, newVoucher] }));

        return newVoucher;
      }

      // Call API to create voucher
      const newVoucher = await vouchersApi.createVoucher(voucherData);

      // Log the created voucher details
      console.log('Voucher created successfully:', newVoucher);
      console.log('Voucher ID:', newVoucher.voucher_id);

      // CRITICAL FIX: Check if the voucher already exists before adding it
      set((state) => {
        // Check if this voucher already exists in the store
        const existingVoucher = state.vouchers.find(
          v => v.id === newVoucher.id || v.voucherId === newVoucher.voucher_id
        );

        if (existingVoucher) {
          console.log('Voucher already exists in store, not adding duplicate:', newVoucher.voucher_id);
          return state; // Return unchanged state
        }

        console.log('Adding new voucher to store:', newVoucher.voucher_id);
        return { vouchers: [...state.vouchers, newVoucher] };
      });

      // Send state update to other clients
      sendStateUpdate({
        type: 'VOUCHER_CREATE',
        voucher: newVoucher
      });

      return newVoucher;
    } catch (error) {
      console.error('Error creating voucher:', error);
      throw error;
    }
  },
  updateVoucher: async (voucherId, voucherData) => {
    try {
    console.log(`Updating voucher ${voucherId} with:`, voucherData);

    // Log status changes for debugging
    const currentVoucher = get().vouchers.find(v => v.id === voucherId);
    if (currentVoucher && voucherData.status && currentVoucher.status !== voucherData.status) {
      console.log(`Voucher ${voucherId} (${currentVoucher.voucherId}) status changing from ${currentVoucher.status} to ${voucherData.status}`);
    }

    // Create a deep copy of the data with consistent string comments to avoid reference issues
    const cleanedData = JSON.parse(JSON.stringify(voucherData));

    // Ensure comments are always strings if present
    if (voucherData.returnComment !== undefined) {
      cleanedData.returnComment = String(voucherData.returnComment);
      console.log(`Normalized returnComment to string: "${cleanedData.returnComment}"`);
    }

    if (voucherData.comment !== undefined) {
      cleanedData.comment = String(voucherData.comment);
      console.log(`Normalized comment to string: "${cleanedData.comment}"`);
    }

    // Clean up numeric fields
    if (voucherData.preAuditedAmount !== undefined) {
      if (typeof voucherData.preAuditedAmount === 'string') {
        const parsed = parseFloat(voucherData.preAuditedAmount);
        if (!isNaN(parsed)) {
          cleanedData.preAuditedAmount = parsed;
          console.log(`Converted preAuditedAmount from string to number: ${parsed}`);
        } else {
          console.warn(`Invalid preAuditedAmount: ${voucherData.preAuditedAmount}`);
          delete cleanedData.preAuditedAmount;
        }
      }
    }

    if (voucherData.taxAmount !== undefined) {
      if (typeof voucherData.taxAmount === 'string') {
        const parsed = parseFloat(voucherData.taxAmount);
        if (!isNaN(parsed)) {
          cleanedData.taxAmount = parsed;
          console.log(`Converted taxAmount from string to number: ${parsed}`);
        } else {
          console.warn(`Invalid taxAmount: ${voucherData.taxAmount}`);
          delete cleanedData.taxAmount;
        }
      }
    }

    // Ensure status is explicitly set for edited vouchers if status is in AUDIT: PROCESSING
    if (cleanedData.status === "AUDIT: PROCESSING") {
      console.log("Ensuring voucher will appear in PENDING DISPATCH tab");
      cleanedData.dispatched = false;
    }

    // Special handling for rejected vouchers
    if (cleanedData.status === "VOUCHER REJECTED") {
      console.log(`Applying REJECTED status to voucher ${voucherId}`);

      // Force these critical properties for rejected vouchers
      cleanedData.deleted = false; // Critical: Never mark rejected vouchers as deleted
      cleanedData.sentToAudit = true; // Keep this flag to ensure it appears in filters

      if (!cleanedData.rejectionTime) {
        cleanedData.rejectionTime = formatCurrentDate();
      }

      console.log(`Final rejected voucher data for ${voucherId}:`, cleanedData);
    }

    // Special handling for vouchers
    // First, ensure we have a string for the comment
    let commentText = "NO COMMENT PROVIDED";

    if (typeof cleanedData.returnComment === 'string' && cleanedData.returnComment.trim().length > 0) {
      commentText = cleanedData.returnComment;
    } else if (typeof cleanedData.comment === 'string' && cleanedData.comment.trim().length > 0) {
      commentText = cleanedData.comment;
    }

    // Case 1: Voucher is explicitly marked as returned
    if (cleanedData.status === "VOUCHER RETURNED" || cleanedData.isReturned === true) {
      console.log(`Applying RETURNED status to voucher ${voucherId}`);

      // Force these critical properties for returned vouchers
      cleanedData.isReturned = true;
      cleanedData.pendingReturn = false; // Clear pending flag when fully returned

      if (!cleanedData.returnTime) {
        cleanedData.returnTime = formatCurrentDate();
      }

      // Make sure both returnComment and comment are properly set as strings
      cleanedData.returnComment = commentText;
      cleanedData.comment = commentText;

      console.log(`Final returned voucher data for ${voucherId}:`, {
        isReturned: cleanedData.isReturned,
        pendingReturn: cleanedData.pendingReturn,
        status: cleanedData.status,
        returnComment: cleanedData.returnComment,
        comment: cleanedData.comment
      });
    }

    // Case 2: Voucher is marked for pending return
    else if (cleanedData.pendingReturn === true) {
      console.log(`Applying PENDING RETURN status to voucher ${voucherId}`);

      // For pending return, ensure isReturned is false
      cleanedData.isReturned = false;
      cleanedData.pendingReturn = true;

      // Set status to AUDIT: PROCESSING to ensure it appears in PENDING DISPATCH
      if (!cleanedData.status || cleanedData.status !== "AUDIT: PROCESSING") {
        cleanedData.status = "AUDIT: PROCESSING";
      }

      if (!cleanedData.returnTime) {
        cleanedData.returnTime = formatCurrentDate();
      }

      // Make sure both returnComment and comment are properly set as strings
      cleanedData.returnComment = commentText;
      cleanedData.comment = commentText;

      console.log(`Final pending return voucher data for ${voucherId}:`, {
        pendingReturn: cleanedData.pendingReturn,
        isReturned: cleanedData.isReturned,
        status: cleanedData.status,
        returnComment: cleanedData.returnComment,
        comment: cleanedData.comment
      });
    }

    // For development/testing, use the local approach
    if (process.env.NODE_ENV === 'development' && !localStorage.getItem('auth_token')) {
      // Apply the update locally
      console.log(`Final update for voucher ${voucherId}:`, cleanedData);

      set((state) => {
        // First, find the voucher to update
        const voucherToUpdate = state.vouchers.find(v => v.id === voucherId);
        if (!voucherToUpdate) {
          console.error(`Voucher ${voucherId} not found for update`);
          return state; // Return unchanged state if voucher not found
        }

        // Create the updated voucher with all fields properly merged
        // Use a deep clone to avoid any reference issues
        const updatedVoucher = { ...JSON.parse(JSON.stringify(voucherToUpdate)), ...cleanedData };

        // Create the updated vouchers array
        const updatedVouchers = state.vouchers.map(voucher =>
          voucher.id === voucherId ? updatedVoucher : voucher
        );

        return { vouchers: updatedVouchers };
      });
    } else {
      // Call API to update voucher
      const updatedVoucher = await vouchersApi.updateVoucher(voucherId, cleanedData);

      // Update local state
      set((state) => ({
        vouchers: state.vouchers.map(voucher =>
          voucher.id === voucherId ? updatedVoucher : voucher
        )
      }));

      // Send state update to other clients
      sendStateUpdate({
        type: 'VOUCHER_UPDATE',
        voucher: updatedVoucher
      });
    }

    // Handle notifications for status changes
    const updatedVoucher = get().vouchers.find(v => v.id === voucherId);
    if (updatedVoucher) {

    // Handle notification for rejection
    if (cleanedData.status === "VOUCHER REJECTED") {
      const voucher = get().vouchers.find(v => v.id === voucherId);
      if (voucher) {
        const departmentUser = get().users.find(u => u.department === voucher.department);
        if (departmentUser) {
          const currentTime = formatCurrentDate();
          const commentText = typeof cleanedData.comment === 'string' ? cleanedData.comment : 'NO COMMENT PROVIDED';

          // Notify department about the rejection
          get().addNotification({
            userId: departmentUser.id,
            message: `VOUCHER ${voucher.voucherId} REJECTED BY AUDIT: ${commentText}`,
            voucherId,
            type: "VOUCHER_REJECTED"
          });
        }
      }
    }

    // If status changed to certified, notify department
    if (cleanedData.status === "VOUCHER CERTIFIED") {
      const voucher = get().vouchers.find(v => v.id === voucherId);
      if (voucher) {
        const departmentUser = get().users.find(u => u.department === voucher.department);
        if (departmentUser) {
          get().addNotification({
            userId: departmentUser.id,
            message: `VOUCHER ${voucher.voucherId} CERTIFIED BY AUDIT`,
            voucherId,
            type: "VOUCHER_CERTIFIED"
          });
        }
      }
    }

    // If voucher is marked as returned or has status VOUCHER RETURNED, notify department
    if (cleanedData.isReturned === true || cleanedData.status === "VOUCHER RETURNED") {
      const voucher = get().vouchers.find(v => v.id === voucherId);
      if (voucher) {
        const departmentUser = get().users.find(u => u.department === voucher.department);
        if (departmentUser) {
          // Extract comment ensuring it's a string
          let commentText = "NO COMMENT PROVIDED";

          if (typeof cleanedData.returnComment === 'string' && cleanedData.returnComment.trim().length > 0) {
            commentText = cleanedData.returnComment;
          } else if (typeof cleanedData.comment === 'string' && cleanedData.comment.trim().length > 0) {
            commentText = cleanedData.comment;
          }

          // Create notification with the comment
          const notificationMessage = `VOUCHER ${voucher.voucherId} RETURNED BY AUDIT: ${commentText}`;

          get().addNotification({
            userId: departmentUser.id,
            message: notificationMessage,
            voucherId,
            type: "VOUCHER_RETURNED"
          });
        }
      }
    }
    }
  } catch (error) {
    console.error('Error updating voucher:', error);
    throw error;
  }
},
  deleteVoucher: async (voucherId) => {
    try {
    // Check if the voucher exists
    const voucher = get().vouchers.find(v => v.id === voucherId);
    if (!voucher) {
      console.log(`Voucher with ID: ${voucherId} not found`);
      return;
    }

    // If it's a rejected voucher - don't actually delete it, mark as deleted
    if (voucher.status === "VOUCHER REJECTED") {
      console.log(`Marking rejected voucher ${voucherId} as deleted`);

      set((state) => ({
        vouchers: state.vouchers.map(v =>
          v.id === voucherId ? { ...v, deleted: true } : v
        )
      }));
      return;
    }

    // For vouchers in PENDING SUBMISSION, just delete them immediately
    if (voucher.status === "PENDING SUBMISSION" && !voucher.sentToAudit) {
      console.log(`Direct deletion of pending voucher with ID: ${voucherId}`);
      set((state) => ({
        vouchers: state.vouchers.filter(v => v.id !== voucherId)
      }));
      return;
    }

    // For development/testing, use the local approach
    if (process.env.NODE_ENV === 'development' && !localStorage.getItem('auth_token')) {
      // Normal deletion for other vouchers
      console.log(`Deleting voucher with ID: ${voucherId}`);
      set((state) => ({
        vouchers: state.vouchers.filter(voucher => voucher.id !== voucherId)
      }));
    } else {
      // Call API to delete voucher
      await vouchersApi.deleteVoucher(voucherId);

      // Update local state
      set((state) => ({
        vouchers: state.vouchers.filter(voucher => voucher.id !== voucherId)
      }));

      // Send state update to other clients
      sendStateUpdate({
        type: 'VOUCHER_DELETE',
        voucherId
      });
    }
  } catch (error) {
    console.error('Error deleting voucher:', error);
    throw error;
  }
},

// Fetch vouchers from API
fetchVouchers: async (department?: string) => {
  try {
    const currentUser = get().currentUser;
    console.log(`Fetching vouchers for department: ${department || (currentUser?.department || 'all')}`);

    // If department is not specified, use the current user's department
    const deptToFetch = department || (currentUser?.department || 'all');

    // Get current vouchers before fetching
    const currentVouchers = get().vouchers;
    console.log(`Current voucher count before fetch: ${currentVouchers.length}`);

    // Call API to get vouchers with a timestamp to prevent caching
    const vouchers = await vouchersApi.getAllVouchers(deptToFetch);

    console.log(`Received ${vouchers.length} vouchers from API for department ${deptToFetch}`);

    // Check if we're missing any vouchers
    if (currentVouchers.length > 0 && vouchers.length > 0) {
      // Get all voucher IDs from current and new vouchers
      const currentIds = new Set(currentVouchers.map(v => v.id));
      const newIds = new Set(vouchers.map(v => v.id));

      // Find missing vouchers
      const missingFromCurrent = [...newIds].filter(id => !currentIds.has(id));
      const missingFromNew = [...currentIds].filter(id => !newIds.has(id) &&
        // Only consider vouchers from the same department or if we're fetching all
        (deptToFetch === 'all' || currentVouchers.find(v => v.id === id)?.department === deptToFetch));

      console.log(`Missing from current: ${missingFromCurrent.length}, Missing from new: ${missingFromNew.length}`);

      if (missingFromCurrent.length > 0) {
        console.log('New vouchers found that were missing from current state:',
          missingFromCurrent.map(id => {
            const v = vouchers.find(v => v.id === id);
            return v ? `${v.voucherId || v.voucher_id} (${v.department})` : id;
          }));
      }

      if (missingFromNew.length > 0) {
        console.log('Vouchers missing from API response that were in current state:',
          missingFromNew.map(id => {
            const v = currentVouchers.find(v => v.id === id);
            return v ? `${v.voucherId || v.voucher_id} (${v.department})` : id;
          }));
      }
    }

    // Log the first few vouchers for debugging
    if (vouchers.length > 0) {
      console.log('First few vouchers:', vouchers.slice(0, 3).map(v => ({
        id: v.id,
        voucherId: v.voucherId || v.voucher_id,
        department: v.department,
        status: v.status,
        sentToAudit: v.sentToAudit || v.sent_to_audit
      })));
    } else {
      console.warn(`No vouchers returned for department: ${deptToFetch}`);
    }

    // Update local state
    set({ vouchers });

    // Log success
    console.log(`Successfully updated store with ${vouchers.length} vouchers for department ${deptToFetch}`);

    return true;
  } catch (error) {
    console.error('Error fetching vouchers:', error);
    return false;
  }
},

// Send voucher to audit
sendVoucherToAudit: async (voucherId) => {
  try {
    // Call API to send voucher to audit
    const updatedVoucher = await vouchersApi.sendToAudit(voucherId);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_UPDATE',
      voucher: updatedVoucher
    });

    return true;
  } catch (error) {
    console.error('Error sending voucher to audit:', error);
    return false;
  }
},

// Return voucher
returnVoucher: async (voucherId, returnComment) => {
  try {
    // Call API to return voucher
    const updatedVoucher = await vouchersApi.returnVoucher(voucherId, returnComment);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_UPDATE',
      voucher: updatedVoucher
    });

    return true;
  } catch (error) {
    console.error('Error returning voucher:', error);
    return false;
  }
},

// Certify voucher
certifyVoucher: async (voucherId) => {
  try {
    // Call API to certify voucher
    const updatedVoucher = await vouchersApi.certifyVoucher(voucherId);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_UPDATE',
      voucher: updatedVoucher
    });

    return true;
  } catch (error) {
    console.error('Error certifying voucher:', error);
    return false;
  }
},

// Reject voucher
rejectVoucher: async (voucherId, comment) => {
  try {
    // Call API to reject voucher
    const updatedVoucher = await vouchersApi.rejectVoucher(voucherId, comment);

    // Update local state
    set((state) => ({
      vouchers: state.vouchers.map(voucher =>
        voucher.id === voucherId ? updatedVoucher : voucher
      )
    }));

    // Send state update to other clients
    sendStateUpdate({
      type: 'VOUCHER_UPDATE',
      voucher: updatedVoucher
    });

    return true;
  } catch (error) {
    console.error('Error rejecting voucher:', error);
    return false;
  }
},
});
