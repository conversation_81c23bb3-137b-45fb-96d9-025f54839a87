import { FiscalMonth } from './types';

// Format the current date as YYYY-MM-DD_HH-MM-SS for file naming
export const formatCurrentDate = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`;
};

// Format date as YYYY-MM-DD for standard use
export const formatStandardDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// Get the current fiscal year based on the fiscal year settings
export const getCurrentFiscalYear = (
  currentDate: Date = new Date(),
  fiscalYearStart: FiscalMonth,
): number => {
  const fiscalMonthMap: Record<FiscalMonth, number> = {
    JAN: 0, FEB: 1, MAR: 2, APR: 3, MAY: 4, JUN: 5,
    JUL: 6, AUG: 7, SEP: 8, OCT: 9, NOV: 10, DEC: 11
  };
  
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const fiscalStartMonth = fiscalMonthMap[fiscalYearStart];
  
  // If the current month is before the fiscal year start month,
  // we're in the previous fiscal year
  if (currentMonth < fiscalStartMonth) {
    return currentYear - 1;
  }
  
  return currentYear;
};

// Convert bytes to readable file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
