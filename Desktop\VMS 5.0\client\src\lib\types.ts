export type Department =
  | "FINANCE"
  | "MINISTRIES"
  | "PENSIONS"
  | "PENTMEDIA"
  | "MISSIONS"
  | "PENTSOS"
  | "AUDIT"
  | "ADMINISTRATOR"
  | "SYSTEM ADMIN";

export type TaxType =
  | ""
  | "NONE"
  | "GOODS 3%"
  | "SERVICE 7.5%"
  | "WORKS 5%"
  | "RENT 8%"
  | "PCC 12.5%"
  | "RISK 5%"
  | "VEH.MAINT 10%"
  | "OTHER";

export type Currency = "GHS" | "USD" | "GBP" | "EUR";

export type ClearanceRemark = "CLEARED" | "REFUNDED TO CHEST" | "DUE STAFF" | "RETURNED";

export type TransactionStatus =
  | "PENDING SUBMISSION"
  | "PENDING RECEIPT"
  | "AUDIT: PROCESSING"
  | "VOUCHER PROCESSING"
  | "VOUCHER CERTIFIED"
  | "VOUCHER REJECTED"
  | "VOUCHER RETURNED"
  | "VOUCHER PENDING RETURN"
  | "PENDING DISPATCH";

export interface User {
  id: string;
  name: string;
  department: Department;
  role: "admin" | "manager" | "operator" | "viewer" | "USER";
}

export interface Voucher {
  id: string;
  voucherId: string;
  date: string;
  claimant: string;
  description: string;
  amount: number;
  currency: Currency;
  department: Department;
  dispatchedBy: string;
  dispatchTime: string;
  status: TransactionStatus;
  sentToAudit: boolean;
  createdBy: string;
  batchId?: string;
  receivedBy?: string;
  receiptTime?: string;
  comment?: string;
  taxType?: TaxType;
  taxDetails?: string;
  taxAmount?: number;
  preAuditedAmount?: number;
  preAuditedBy?: string;
  certifiedBy?: string;
  auditDispatchTime?: string;
  auditDispatchedBy?: string;
  dispatchToOnDepartment?: boolean;
  postProvisionalCash?: boolean;
  dispatched?: boolean;
  dispatchToAuditBy?: string;
  isReturned?: boolean;
  returnComment?: string;
  returnTime?: string;
  deleted?: boolean;
  deletionTime?: string;
  rejectionTime?: string;
  departmentReceiptTime?: string;
  departmentReceivedBy?: string;
  departmentRejected?: boolean;
  rejectedBy?: string;
  pendingReturn?: boolean;
  returnInitiatedTime?: string;
  referenceId?: string;
}

export interface VoucherBatch {
  id: string;
  department: Department;
  voucherIds: string[];
  sentBy: string;
  sentTime: string;
  received: boolean;
  fromAudit?: boolean; // Indicates if the batch was created by Audit to send to department
}

export interface ProvisionalCashRecord {
  id: string;
  voucherId: string;
  voucherRef: string;
  claimant: string;
  description: string;
  mainAmount: number;
  currency: Currency;
  amountRetired?: number;
  clearanceRemark?: ClearanceRemark;
  dateRetired?: string;
  clearedBy?: string;
  comment?: string;
  date: string; // Required field
}

export interface Notification {
  id: string;
  userId: string;
  message: string;
  isRead: boolean;
  timestamp: string;
  voucherId?: string;
  batchId?: string;
  type: "NEW_VOUCHER" | "VOUCHER_REJECTED" | "VOUCHER_CERTIFIED" | "VOUCHER_RETURNED" | "NEW_BATCH" | "OTHER";
  fromAudit?: boolean; // Indicates if the notification is for a batch from Audit to department
}

export interface BlacklistedVoucherId {
  id: string;
  voucherId: string;
}
