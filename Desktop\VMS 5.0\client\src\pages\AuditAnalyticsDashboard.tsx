import { useAppStore } from '@/lib/store';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { ExitButton } from '@/components/exit-button';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { AnalyticsDashboardContent } from '@/components/analytics-dashboard/analytics-dashboard-content';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export default function AuditAnalyticsDashboard() {
  const currentUser = useAppStore((state) => state.currentUser);
  const navigate = useNavigate();

  if (!currentUser) {
    return null;
  }

  const handleBack = () => {
    navigate('/audit-dashboard');
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center px-4 sm:px-6">
          <Button variant="outline" size="icon" onClick={handleBack} className="border-primary hover:bg-primary/10 mr-2">
            <ArrowLeft className="h-5 w-5 text-primary" />
          </Button>
          <h1 className="text-lg font-semibold uppercase">
            AUDIT ANALYTICS DASHBOARD
          </h1>
          <div className="ml-auto flex items-center space-x-4">
            <NotificationsMenu />
            <ModeToggle />
            <UserNav />
            <ExitButton />
          </div>
        </div>
      </header>

      <main className="flex-1 py-6 px-4 sm:px-6">
        <AnalyticsDashboardContent />
      </main>

      <DashboardFooter />
    </div>
  );
}
