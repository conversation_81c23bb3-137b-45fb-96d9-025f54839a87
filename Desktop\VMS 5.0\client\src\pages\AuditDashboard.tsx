
import { useAppStore } from '@/lib/store';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { ExitButton } from '@/components/exit-button';
import { AuditDashboardContent } from '@/components/audit-dashboard/audit-dashboard-content-new';
import { AuditNavigation } from '@/components/audit-dashboard/audit-navigation';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { useEffect } from 'react';

export default function AuditDashboard() {
  const currentUser = useAppStore((state) => state.currentUser);

  useEffect(() => {
    if (!currentUser) {
      window.location.href = '/';
    } else if (currentUser.department !== 'AUDIT') {
      window.location.href = '/dashboard';
    }
  }, [currentUser]);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <header className="border-b">
        <div className="container flex h-16 items-center px-4 sm:px-6">
          <h1 className="text-lg font-semibold uppercase">
            AUDIT DEPARTMENT DASHBOARD
          </h1>
          <div className="ml-auto flex items-center space-x-4">
            <NotificationsMenu />
            <ModeToggle />
            <UserNav />
            <ExitButton />
          </div>
        </div>
      </header>

      <main className="flex-1 py-6 px-4 sm:px-6">
        <AuditNavigation />
        <AuditDashboardContent />
      </main>

      <DashboardFooter />
    </div>
  );
}
