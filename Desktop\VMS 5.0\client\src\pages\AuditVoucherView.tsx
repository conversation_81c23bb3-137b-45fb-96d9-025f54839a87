import { useEffect, useState } from 'react';
import { formatCurrentDate } from '@/utils/formatUtils';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, File, Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/store';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { TaxType, Currency } from '@/lib/types';
import { taxTypes, currencies } from '@/lib/data';
import { Checkbox } from '@/components/ui/checkbox';

export default function AuditVoucherView() {
  const { voucherId } = useParams<{ voucherId: string }>();
  const navigate = useNavigate();

  const currentUser = useAppStore((state) => state.currentUser);
  const vouchers = useAppStore((state) => state.vouchers);
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const addProvisionalCashRecord = useAppStore((state) => state.addProvisionalCashRecord);
  const voucher = vouchers.find(v => v.id === voucherId);

  const [comment, setComment] = useState('');
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [preAuditedAmount, setPreAuditedAmount] = useState<number | undefined>(undefined);
  const [taxType, setTaxType] = useState<TaxType | ''>('');
  const [taxAmount, setTaxAmount] = useState<number | undefined>(undefined);
  const [postProvisionalCash, setPostProvisionalCash] = useState(false);
  const [currency, setCurrency] = useState<Currency>("GHS");

  useEffect(() => {
    if (!currentUser) {
      navigate('/');
    }
  }, [currentUser, navigate]);

  useEffect(() => {
    if (currentUser && currentUser.department !== 'AUDIT') {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  useEffect(() => {
    if (!voucher) {
      toast.error('Voucher not found', {
        duration: 3000,
      });
      navigate('/audit-dashboard');
    }
  }, [voucher, navigate]);

  useEffect(() => {
    if (voucher) {
      setPreAuditedAmount(voucher.preAuditedAmount);
      setTaxType(voucher.taxType || '');
      setTaxAmount(voucher.taxAmount);
      setComment(voucher.comment || '');
      setPostProvisionalCash(voucher.postProvisionalCash || false);
      setCurrency(voucher.currency || "GHS");
    }
  }, [voucher]);

  const getStatusBadge = () => {
    if (!voucher) return null;

    switch (voucher.status) {
      case 'VOUCHER CERTIFIED':
        return <Badge variant="success" className="ml-2">Certified</Badge>;
      case 'VOUCHER REJECTED':
        return <Badge variant="destructive" className="ml-2">Rejected</Badge>;
      case 'AUDIT: PROCESSING':
        return <Badge variant="warning" className="ml-2">Processing</Badge>;
      case 'PENDING RECEIPT':
        return <Badge variant="warning" className="ml-2">Pending Receipt</Badge>;
      case 'PENDING SUBMISSION':
        return <Badge variant="outline" className="ml-2">Pending Submission</Badge>;
      default:
        return null;
    }
  };

  const handleAction = () => {
    if (!selectedAction) {
      toast.error('Action required', {
        duration: 3000,
      });
      return;
    }

    if (selectedAction === 'reject' && !comment) {
      toast.error('Comment required for rejection', {
        duration: 3000,
      });
      return;
    }

    setIsSubmitting(true);

    setTimeout(() => {
      try {
        if (selectedAction === 'certify') {
          // Create a complete update object
          const updateData: Partial<Voucher> = {
            preAuditedAmount,
            taxType: taxType as TaxType,
            taxAmount,
            comment,
            currency,
            status: 'VOUCHER CERTIFIED',
            certifiedBy: currentUser?.name,
            preAuditedBy: currentUser?.name, // Also set preAuditedBy to ensure it's always populated
            auditDispatchTime: formatCurrentDate(),
            auditDispatchedBy: currentUser?.name,
            postProvisionalCash,
            dispatched: true // Mark as dispatched
          };

          // Update the voucher with all fields at once
          console.log('Certifying voucher with data:', updateData);
          updateVoucher(voucherId!, updateData);

          // If provisional cash is checked, create a provisional cash record
          if (postProvisionalCash && voucher) {
            const currentDate = formatCurrentDate();

            addProvisionalCashRecord({
              voucherId: voucher.id,
              voucherRef: voucher.voucherId,
              claimant: voucher.claimant,
              description: voucher.description,
              mainAmount: preAuditedAmount || voucher.amount,
              currency: voucher.currency,
              date: currentDate
            });
          }

          toast.success('Voucher certified successfully', {
            duration: 3000,
          });
        } else if (selectedAction === 'reject') {
          const currentDate = formatCurrentDate();

          updateVoucher(voucherId!, {
            status: 'VOUCHER REJECTED',
            comment,
            rejectedBy: currentUser?.name,
            rejectionTime: currentDate,
            deleted: false
          });

          console.log(`Rejecting voucher ${voucherId}: setting status to VOUCHER REJECTED and deleted to false`);

          toast.success('Voucher rejected successfully', {
            duration: 3000,
          });
        }

        navigate('/audit-dashboard');
      } catch (error) {
        toast.error('Action failed', {
          duration: 3000,
        });
      } finally {
        setIsSubmitting(false);
      }
    }, 1000);
  };

  const handleSaveChanges = () => {
    try {
      // Create a clean update object
      const updateData: Partial<Voucher> = {
        preAuditedAmount,
        taxType: taxType as TaxType,
        taxAmount,
        comment,
        postProvisionalCash,
        // Set status to AUDIT: PROCESSING to ensure it appears in PENDING DISPATCH
        status: "AUDIT: PROCESSING"
      };

      // Always set preAuditedBy and certifiedBy if they're not already set
      if (!voucher?.preAuditedBy) {
        updateData.preAuditedBy = currentUser?.name;
      }

      if (!voucher?.certifiedBy) {
        updateData.certifiedBy = currentUser?.name;
      }

      console.log('Saving voucher changes:', updateData);
      updateVoucher(voucherId!, updateData);

      // Verify the update was applied
      setTimeout(() => {
        const updatedVoucher = vouchers.find(v => v.id === voucherId);
        if (updatedVoucher) {
          console.log(`After saving changes, voucher ${voucherId} state:`, {
            preAuditedAmount: updatedVoucher.preAuditedAmount,
            preAuditedBy: updatedVoucher.preAuditedBy,
            certifiedBy: updatedVoucher.certifiedBy,
            status: updatedVoucher.status
          });
        }
      }, 100);

      toast.success('Changes saved successfully', {
        duration: 3000,
      });
    } catch (error) {
      toast.error('Failed to save changes', {
        duration: 3000,
      });
    }
  };

  const handleBack = () => {
    navigate('/audit-dashboard');
  };

  const canTakeAction = voucher && voucher.status === "AUDIT: PROCESSING";

  if (!currentUser || !voucher) {
    return null;
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b">
        <div className="container flex h-16 items-center px-4 sm:px-6">
          <Button variant="outline" size="icon" onClick={handleBack} className="border-primary hover:bg-primary/10">
            <ArrowLeft className="h-5 w-5 text-primary" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">
            Audit Voucher Review {getStatusBadge()}
          </h1>
          <div className="ml-auto flex items-center space-x-4">
            <NotificationsMenu />
            <ModeToggle />
            <UserNav />
          </div>
        </div>
      </header>

      <main className="flex-1 py-6 px-4 sm:px-6">
        <div className="container space-y-6 max-w-4xl">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Voucher {voucher.voucherId}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">DEPARTMENT</div>
                  <div>{voucher.department}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">DATE</div>
                  <div>{voucher.date}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">CLAIMANT</div>
                  <div>{voucher.claimant}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">AMOUNT</div>
                  <div className="flex items-center gap-2">
                    <div className="font-bold">{voucher.amount.toFixed(2)}</div>
                    <Select value={currency} onValueChange={(value) => setCurrency(value as Currency)}>
                      <SelectTrigger className="w-[100px]">
                        <SelectValue placeholder="Currency" />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.map((c) => (
                          <SelectItem key={c} value={c}>{c}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">CERTIFIED AMT</div>
                  <Input
                    type="number"
                    value={preAuditedAmount || ''}
                    onChange={(e) => setPreAuditedAmount(e.target.value ? Number(e.target.value) : undefined)}
                    placeholder="Enter certified amount"
                  />
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">PROVISIONAL CASH</div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="provisional-cash"
                      checked={postProvisionalCash}
                      onCheckedChange={(checked) => setPostProvisionalCash(!!checked)}
                    />
                    <label htmlFor="provisional-cash" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Add to Provisional Cash Records
                    </label>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">TAX TYPE</div>
                  <Select value={taxType} onValueChange={(value) => setTaxType(value as TaxType)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tax type" />
                    </SelectTrigger>
                    <SelectContent>
                      {taxTypes.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">TAX AMOUNT</div>
                  <Input
                    type="number"
                    value={taxAmount || ''}
                    onChange={(e) => setTaxAmount(e.target.value ? Number(e.target.value) : undefined)}
                    placeholder="Enter tax amount"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">DESCRIPTION</div>
                <div className="p-4 bg-muted rounded-md">{voucher.description}</div>
              </div>

              <div className="space-y-4">
                <div className="text-sm font-medium">TRACKING INFORMATION</div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <File className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Created by:</span>
                    <span>{voucher.dispatchedBy}</span>
                    <span className="text-muted-foreground ml-auto">{voucher.dispatchTime}</span>
                  </div>

                  {voucher.receivedBy && (
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Received by Audit:</span>
                      <span>{voucher.receivedBy}</span>
                      <span className="text-muted-foreground ml-auto">{voucher.receiptTime}</span>
                    </div>
                  )}

                  {voucher.status === 'VOUCHER REJECTED' && (
                    <div className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-4 w-4 text-destructive" />
                      <span className="text-muted-foreground">Rejected by:</span>
                      <span>{voucher.rejectedBy}</span>
                      <span className="text-destructive ml-2">{voucher.comment}</span>
                      <span className="text-muted-foreground ml-auto">{voucher.rejectionTime}</span>
                    </div>
                  )}

                  {voucher.status === 'VOUCHER CERTIFIED' && (
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-emerald-500" />
                      <span className="text-muted-foreground">Certified by:</span>
                      <span>{voucher.certifiedBy}</span>
                      <span className="text-muted-foreground ml-auto">{voucher.auditDispatchTime}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="comment">COMMENT {selectedAction === 'reject' && '(Required)'}</Label>
                <Textarea
                  id="comment"
                  placeholder={selectedAction === 'reject' ? 'Enter reason for rejection' : 'Optional comment'}
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                />
              </div>
            </CardContent>

            <CardFooter className="flex justify-between">
              <div>
                <Button variant="outline" onClick={handleBack} className="mr-2 border-primary hover:bg-primary/10 text-primary">
                  <ArrowLeft className="h-4 w-4 mr-1" /> Back
                </Button>
                <Button variant="secondary" onClick={handleSaveChanges}>
                  Save Changes
                </Button>
              </div>

              {canTakeAction && (
                <div className="flex gap-2">
                  <Button
                    variant="destructive"
                    onClick={() => {
                      setSelectedAction('reject');
                      handleAction();
                    }}
                    disabled={isSubmitting || !comment}
                  >
                    {isSubmitting && selectedAction === 'reject' ? 'Rejecting...' : 'Reject Voucher'}
                  </Button>
                  <Button
                    onClick={() => {
                      setSelectedAction('certify');
                      handleAction();
                    }}
                    disabled={isSubmitting}
                  >
                    {isSubmitting && selectedAction === 'certify' ? 'Certifying...' : 'Certify Voucher'}
                  </Button>
                </div>
              )}
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  );
}
