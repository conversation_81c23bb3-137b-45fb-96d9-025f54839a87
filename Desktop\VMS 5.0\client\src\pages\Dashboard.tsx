
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { useDashboardState } from '@/hooks/use-dashboard-state';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { NewVoucherForm } from '@/components/dashboard/new-voucher-form';
import { DashboardContent } from '@/components/dashboard/dashboard-content';
import { DashboardModals } from '@/components/dashboard/dashboard-modals';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { Department } from '@/lib/types';
import { useDepartmentData } from '@/hooks/use-department-data';

export default function Dashboard() {
  const navigate = useNavigate();
  const {
    currentUser,
    selectedVouchers,
    setSelectedVouchers,
    showVoucherReceiving,
    setShowVoucherReceiving,
    receivingVoucherIds,
    setReceivingVoucherIds,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    viewingVoucher,
    setViewingVoucher,
    showBatchReceiving,
    setShowBatchReceiving,
    selectedBatchId,
    setSelectedBatchId,
    voucherView,
    setVoucherView,
    isNotificationBlinking,
    refreshTrigger,
    refreshData,
    handleDisabledFormClick
  } = useDashboardState();

  const { batchesArray } = useDepartmentData(currentUser?.department as Department, refreshTrigger);

  const hasVouchersToReceive = batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers.some(v => v.certifiedBy || v.status === "VOUCHER REJECTED")
    );

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      <DashboardHeader />

      <div className="px-6 py-2 bg-black">
        <NewVoucherForm
          department={currentUser.department}
          isDisabled={hasVouchersToReceive}
          onDisabledClick={handleDisabledFormClick}
          hidden={hasVouchersToReceive}
        />
      </div>

      <DashboardContent
        department={currentUser.department}
        refreshTrigger={refreshTrigger}
        onRefresh={refreshData}
        onReceiveVouchers={(voucherIds) => {
          setReceivingVoucherIds(voucherIds);
          setShowVoucherReceiving(true);
        }}
        selectedVouchers={selectedVouchers}
        dispatchedBy={dispatchedBy}
        customDispatchName={customDispatchName}
        onDispatcherChange={setDispatchedBy}
        onCustomDispatchNameChange={setCustomDispatchName}
        onSendToAudit={() => {
          const sendVouchersToAudit = useAppStore.getState().sendVouchersToAudit;

          if (selectedVouchers.length > 0 && (dispatchedBy || customDispatchName)) {
            const finalDispatchedBy = dispatchedBy || customDispatchName.toUpperCase();
            sendVouchersToAudit(currentUser.department, selectedVouchers, finalDispatchedBy);

            setSelectedVouchers([]);
            setDispatchedBy('');
            setCustomDispatchName('');
            setVoucherView('processing');
          }
        }}
        onSelectionChange={setSelectedVouchers}
        onViewVoucher={setViewingVoucher}
        voucherView={voucherView}
        onVoucherViewChange={setVoucherView}
        isNotificationBlinking={isNotificationBlinking}
      />

      <DashboardModals
        viewingVoucher={viewingVoucher}
        showVoucherReceiving={showVoucherReceiving}
        receivingVoucherIds={receivingVoucherIds}
        showBatchReceiving={showBatchReceiving}
        selectedBatchId={selectedBatchId}
        onCloseViewingVoucher={() => setViewingVoucher(null)}
        onCloseVoucherReceiving={() => {
          setShowVoucherReceiving(false);
          setReceivingVoucherIds([]);
          refreshData();
          setVoucherView('certified');
        }}
        onCloseBatchReceiving={() => {
          setShowBatchReceiving(false);
          setSelectedBatchId('');
          refreshData();
          setVoucherView('certified');
        }}
      />
      <DashboardFooter />
    </div>
  );
}
