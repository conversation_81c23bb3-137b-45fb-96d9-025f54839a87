import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { usersApi } from '@/lib/api';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Department } from '@/lib/types';
import { departments } from '@/lib/data';
import { toast } from 'sonner';
import { LogoSignature } from '@/components/logo-signature';
import { Eye, EyeOff } from 'lucide-react';
import { ModeToggle } from '@/components/mode-toggle';

export default function Login() {
  const navigate = useNavigate();
  const login = useAppStore((state) => state.login);

  const [department, setDepartment] = useState<Department | ''>('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [departmentUsers, setDepartmentUsers] = useState<any[]>([]);

  // Fetch all users when component mounts and periodically refresh
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        // Create a simple API endpoint to get users without authentication
        const response = await fetch('/api/auth/users-by-department');
        if (response.ok) {
          const users = await response.json();
          setAllUsers(users);
          console.log('Fetched users for login:', users.length);
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        // Fallback to hardcoded admin user if API fails
        setAllUsers([{ id: 'admin-default', name: 'System Administrator', department: 'SYSTEM ADMIN' }]);
      }
    };

    // Fetch immediately on mount
    fetchUsers();

    // Then set up an interval to refresh every 5 seconds
    const intervalId = setInterval(fetchUsers, 5000);

    // Clean up the interval when component unmounts
    return () => clearInterval(intervalId);
  }, []);

  // Filter users by department when department changes
  useEffect(() => {
    if (department) {
      const filteredUsers = allUsers.filter(user =>
        user.department.toUpperCase() === department.toUpperCase()
      );
      setDepartmentUsers(filteredUsers);
    } else {
      setDepartmentUsers([]);
    }
  }, [department, allUsers]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if all required fields are filled
    if (!department || !password) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Make sure username is selected
    if (!username) {
      toast.error('Please select a username');
      return;
    }

    setIsLoading(true);

    try {
      // Use backend authentication
      const success = await login(department as Department, username, password, false);

      if (success) {
        // Get the current user from the store
        const currentUser = useAppStore.getState().currentUser;

        // Route based on department
        if (currentUser) {
          if (currentUser.department === 'AUDIT') {
            navigate('/audit-dashboard');
          } else if (currentUser.department === 'SYSTEM ADMIN') {
            navigate('/admin-dashboard');
          } else {
            navigate('/dashboard');
          }
        }
      } else {
        toast.error('Invalid credentials. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-slate-50 dark:bg-slate-900 relative">
      <div className="absolute top-4 right-4">
        <ModeToggle />
      </div>
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            Voucher Management System
          </CardTitle>
          <CardDescription className="text-center">
            Sign in to continue to the dashboard
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleLogin}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select
                value={department}
                onValueChange={(value: string) => {
                  setDepartment(value as Department);
                  setUsername('');
                }}
              >
                <SelectTrigger id="department">
                  <SelectValue placeholder="Select your department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Select
                value={username}
                onValueChange={setUsername}
                disabled={!department}
              >
                <SelectTrigger id="username">
                  <SelectValue placeholder="Select your username" />
                </SelectTrigger>
                <SelectContent>
                  {departmentUsers.map((user) => (
                    <SelectItem key={user.id} value={user.name}>
                      {user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button className="w-full" type="submit" disabled={isLoading}>
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
            <div className="text-center text-sm">
              <span className="text-muted-foreground">Don't have an account? </span>
              <Button variant="link" className="p-0" onClick={() => navigate('/register')}>
                Register here
              </Button>
            </div>
          </CardFooter>
        </form>
      </Card>

      <div className="mt-8">
        <LogoSignature size="md" variant="light" />
      </div>
    </div>
  );
}
