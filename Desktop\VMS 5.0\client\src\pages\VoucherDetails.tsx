
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, File, Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/store';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { ExitButton } from '@/components/exit-button';
import { toast } from '@/hooks/use-toast';

export default function VoucherDetails() {
  const { voucherId } = useParams<{ voucherId: string }>();
  const navigate = useNavigate();

  const currentUser = useAppStore((state) => state.currentUser);
  const vouchers = useAppStore((state) => state.vouchers);
  const voucher = vouchers.find(v => v.id === voucherId);

  // Redirect if not logged in
  useEffect(() => {
    if (!currentUser) {
      navigate('/');
    }
  }, [currentUser, navigate]);

  // Go back to appropriate dashboard if voucher not found
  useEffect(() => {
    if (!voucher) {
      toast({
        title: 'Voucher not found',
        description: 'The requested voucher could not be found',
        variant: 'destructive',
      });

      if (currentUser?.department === 'AUDIT') {
        navigate('/audit-dashboard');
      } else if (currentUser?.department === 'ADMINISTRATOR') {
        navigate('/admin-dashboard');
      } else {
        navigate('/dashboard');
      }
    }
  }, [voucher, currentUser, navigate]);

  const getStatusBadge = () => {
    if (!voucher) return null;

    switch (voucher.status) {
      case 'VOUCHER CERTIFIED':
        return <Badge variant="success" className="ml-2">Certified</Badge>;
      case 'VOUCHER REJECTED':
        return <Badge variant="destructive" className="ml-2">Rejected</Badge>;
      case 'AUDIT: PROCESSING':
        return <Badge variant="warning" className="ml-2">Processing</Badge>;
      case 'PENDING RECEIPT':
        return <Badge variant="warning" className="ml-2">Pending Receipt</Badge>;
      case 'PENDING SUBMISSION':
        return <Badge variant="outline" className="ml-2">Pending Submission</Badge>;
      default:
        return null;
    }
  };

  const handleBack = () => {
    if (currentUser?.department === 'AUDIT') {
      navigate('/audit-dashboard');
    } else if (currentUser?.department === 'ADMINISTRATOR') {
      navigate('/admin-dashboard');
    } else {
      navigate('/dashboard');
    }
  };

  if (!currentUser || !voucher) {
    return null;
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b">
        <div className="container flex h-16 items-center px-4 sm:px-6">
          <Button variant="outline" size="icon" onClick={handleBack} className="border-primary hover:bg-primary/10">
            <ArrowLeft className="h-5 w-5 text-primary" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">
            Voucher Details {getStatusBadge()}
          </h1>
          <div className="ml-auto flex items-center space-x-4">
            <NotificationsMenu />
            <ModeToggle />
            <UserNav />
            <ExitButton />
          </div>
        </div>
      </header>

      <main className="flex-1 py-6 px-4 sm:px-6">
        <div className="container space-y-6 max-w-4xl">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Voucher {voucher.voucherId}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Department</div>
                  <div>{voucher.department}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Date</div>
                  <div>{voucher.date}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Claimant</div>
                  <div>{voucher.claimant}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Amount</div>
                  <div className="font-bold">{voucher.amount.toFixed(2)}</div>
                </div>
                {voucher.taxType && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">Tax Type</div>
                    <div>{voucher.taxType}</div>
                  </div>
                )}
                {voucher.taxDetails && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">Tax Details</div>
                    <div>{voucher.taxDetails}</div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Description</div>
                <div className="p-4 bg-muted rounded-md">{voucher.description}</div>
              </div>

              <div className="space-y-4">
                <div className="text-sm font-medium">Tracking Information</div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <File className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Created by:</span>
                    <span>{voucher.dispatchedBy}</span>
                    <span className="text-muted-foreground ml-auto">{voucher.dispatchTime}</span>
                  </div>

                  {voucher.receivedBy && (
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Received by Audit:</span>
                      <span>{voucher.receivedBy}</span>
                      <span className="text-muted-foreground ml-auto">{voucher.receiptTime}</span>
                    </div>
                  )}

                  {voucher.status === 'VOUCHER REJECTED' && (
                    <div className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-4 w-4 text-destructive" />
                      <span className="text-muted-foreground">Rejected:</span>
                      <span className="text-destructive">{voucher.comment}</span>
                    </div>
                  )}

                  {voucher.status === 'VOUCHER CERTIFIED' && (
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-emerald-500" />
                      <span className="text-muted-foreground">Certified by:</span>
                      <span>{voucher.certifiedBy}</span>
                      <span className="text-muted-foreground ml-auto">{voucher.auditDispatchTime}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
