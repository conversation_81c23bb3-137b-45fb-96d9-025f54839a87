import { Voucher } from '@/lib/types';

/**
 * Exports vouchers to Excel format
 * @param vouchers The vouchers to export
 * @param filename The name of the exported file
 */
export const exportVouchersToExcel = (vouchers: Voucher[], filename: string = 'vouchers-export') => {
  // In a real application, this would use a library like xlsx or exceljs
  // For this simulation, we'll create a CSV string and download it
  
  // Create CSV header
  const headers = [
    'Voucher ID',
    'Date',
    'Claimant',
    'Description',
    'Amount',
    'Currency',
    'Department',
    'Status',
    'Dispatched By',
    'Dispatch Time'
  ];
  
  // Create CSV rows from vouchers
  const rows = vouchers.map(voucher => [
    voucher.voucherId,
    voucher.date,
    voucher.claimant,
    voucher.description,
    voucher.amount.toString(),
    voucher.currency,
    voucher.department,
    voucher.status,
    voucher.dispatchedBy || 'N/A',
    voucher.dispatchTime || 'N/A'
  ]);
  
  // Combine headers and rows
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n');
  
  // Create a Blob with the CSV content
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // Create a download link
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  
  // Add the link to the DOM
  document.body.appendChild(link);
  
  // Click the link to trigger the download
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  
  console.log(`Exported ${vouchers.length} vouchers to ${filename}.csv`);
};

/**
 * Groups vouchers by month
 * @param vouchers The vouchers to group
 * @returns An object with months as keys and arrays of vouchers as values
 */
export const groupVouchersByMonth = (vouchers: Voucher[]) => {
  const months = [
    'January', 'February', 'March', 'April', 
    'May', 'June', 'July', 'August', 
    'September', 'October', 'November', 'December'
  ];
  
  // Initialize the result object with empty arrays for each month
  const result: Record<string, Voucher[]> = {};
  months.forEach(month => {
    result[month] = [];
  });
  
  // Group vouchers by month
  vouchers.forEach(voucher => {
    // Extract month from the date string
    // Assuming date format like "28-APRIL-2025 10:00 AM" or similar
    const dateString = voucher.date;
    
    // Find which month is mentioned in the date string
    const matchedMonth = months.find(month => 
      dateString.toUpperCase().includes(month.toUpperCase())
    );
    
    if (matchedMonth) {
      result[matchedMonth].push(voucher);
    } else {
      // If no month is found, try to extract it from the date
      try {
        // Try to parse the date
        const dateParts = dateString.split(/[-\s]/);
        // Check if we have a numeric month
        if (dateParts.length >= 2 && !isNaN(Number(dateParts[1]))) {
          const monthIndex = Number(dateParts[1]) - 1; // 0-based index
          if (monthIndex >= 0 && monthIndex < 12) {
            result[months[monthIndex]].push(voucher);
          }
        }
      } catch (error) {
        console.error('Error parsing date:', dateString, error);
      }
    }
  });
  
  return result;
};
