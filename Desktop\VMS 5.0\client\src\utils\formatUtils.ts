
export const formatCurrentDate = () => {
  const now = new Date();
  const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
  const month = monthNames[now.getMonth()];
  const day = now.getDate();
  const year = now.getFullYear();

  const hours = now.getHours();
  const minutes = now.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const formattedHours = hours % 12 || 12;

  return `${month} ${day}, ${year} AT ${formattedHours}:${minutes.toString().padStart(2, '0')}${ampm}`;
};

export const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '—';

  // Handle different date formats
  try {
    // Try to parse the date
    let date: Date;
    let hours = 0;
    let minutes = 0;
    let hasTime = false;

    // Check if it's an ISO date string (e.g., "2025-04-06T18:59:23.123Z")
    if (dateString.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
      try {
        date = new Date(dateString);
        hasTime = true;
        hours = date.getHours();
        minutes = date.getMinutes();
      } catch (error) {
        console.error('Error parsing ISO date:', error);
        return dateString;
      }
    }
    // Check if it's in the format "28-APRIL-2025" or "28-APRIL-2025 10:00 AM"
    else if (dateString.includes('-')) {
      const dateParts = dateString.split(' ');
      const dateOnly = dateParts[0].split('-');
      const day = parseInt(dateOnly[0]);
      const monthStr = dateOnly[1];
      const year = parseInt(dateOnly[2]);

      // Convert month name to month number
      const monthNames = ['JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE', 'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'];
      const month = monthNames.findIndex(m => m === monthStr);

      if (month !== -1) {
        date = new Date(year, month, day);

        // Check if there's time information
        if (dateParts.length > 1) {
          hasTime = true;
          const timePart = dateParts[1];
          const timeOnly = timePart.split(':');
          let hourValue = parseInt(timeOnly[0]);
          const minuteValue = parseInt(timeOnly[1].split(' ')[0]);
          const ampm = timeOnly[1].includes('PM') ? 'PM' : 'AM';

          // Convert to 24-hour format if PM
          if (ampm === 'PM' && hourValue < 12) {
            hourValue += 12;
          } else if (ampm === 'AM' && hourValue === 12) {
            hourValue = 0;
          }

          hours = hourValue;
          minutes = minuteValue;
        }
      } else {
        // If we can't parse it, just return the original string
        return dateString;
      }
    } else if (dateString.includes('AT')) {
      // Format is already "MMM DD, YYYY AT HH:MMAM/PM"
      return dateString;
    } else {
      // Assume it's already in a standard format
      return dateString;
    }

    // Format the date in the standard format
    const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();

    if (hasTime) {
      // Format with time
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = hours % 12 || 12;
      return `${month} ${day}, ${year} AT ${formattedHours}:${minutes.toString().padStart(2, '0')}${ampm}`;
    } else {
      // Format date only, but add a default time to match the NEW VOUCHERS format
      return `${month} ${day}, ${year} AT 12:00AM`;
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

export const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

export const formatNumberWithCommas = (amount: number | undefined) => {
  if (amount === undefined) return '-';
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2
  }).format(amount);
};
