import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to run a command
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command} ${args.join(' ')}`);
    
    const process = spawn(command, args, {
      stdio: 'inherit',
      ...options
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Function to start a process in the background
function startProcess(command, args, options = {}) {
  console.log(`Starting: ${command} ${args.join(' ')}`);
  
  const process = spawn(command, args, {
    stdio: 'inherit',
    detached: true,
    ...options
  });
  
  process.unref();
  
  return process;
}

// Main function
async function main() {
  try {
    // Check if server node_modules exists
    if (!fs.existsSync(path.join(__dirname, 'server', 'node_modules'))) {
      console.log('Installing server dependencies...');
      await runCommand('npm', ['install'], { cwd: path.join(__dirname, 'server') });
    }
    
    // Check if client node_modules exists
    if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
      console.log('Installing client dependencies...');
      await runCommand('npm', ['install']);
    }
    
    // Start server in the background
    console.log('Starting server...');
    startProcess('node', ['server/start-server.js']);
    
    // Wait for server to start
    console.log('Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Start client
    console.log('Starting client...');
    await runCommand('npm', ['run', 'dev']);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
