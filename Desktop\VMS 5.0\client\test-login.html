<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Test Login</h1>
    <div class="form-group">
        <label for="department">Department:</label>
        <select id="department">
            <option value="SYSTEM ADMIN">SYSTEM ADMIN</option>
        </select>
    </div>
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" value="System Administrator">
    </div>
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="admin123">
    </div>
    <button onclick="testLogin()">Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const department = document.getElementById('department').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = 'Attempting to login...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        department,
                        username,
                        password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>Login Successful!</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>Login Failed</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
