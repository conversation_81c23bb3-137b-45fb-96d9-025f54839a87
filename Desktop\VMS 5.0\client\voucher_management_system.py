import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

class VoucherManagementSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("Voucher Management System")
        self.root.geometry("800x600")
        
        # Initialize data storage
        self.vouchers = []
        self.load_vouchers()
        
        # Create main frame
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create widgets
        self.create_widgets()
        
    def create_widgets(self):
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create tabs
        self.create_voucher_tab = ttk.Frame(self.notebook)
        self.view_vouchers_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.create_voucher_tab, text="Create Voucher")
        self.notebook.add(self.view_vouchers_tab, text="View Vouchers")
        
        # Setup create voucher tab
        self.setup_create_voucher_tab()
        
        # Setup view vouchers tab
        self.setup_view_vouchers_tab()
        
    def setup_create_voucher_tab(self):
        # Create voucher form
        ttk.Label(self.create_voucher_tab, text="Voucher Code:").grid(row=0, column=0, padx=5, pady=5)
        self.voucher_code = ttk.Entry(self.create_voucher_tab)
        self.voucher_code.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.create_voucher_tab, text="Discount Amount:").grid(row=1, column=0, padx=5, pady=5)
        self.discount_amount = ttk.Entry(self.create_voucher_tab)
        self.discount_amount.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.create_voucher_tab, text="Expiry Date:").grid(row=2, column=0, padx=5, pady=5)
        self.expiry_date = ttk.Entry(self.create_voucher_tab)
        self.expiry_date.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Button(self.create_voucher_tab, text="Create Voucher", 
                  command=self.create_voucher).grid(row=3, column=0, columnspan=2, pady=10)
        
    def setup_view_vouchers_tab(self):
        # Create treeview for vouchers
        columns = ("Code", "Discount", "Expiry", "Status")
        self.voucher_tree = ttk.Treeview(self.view_vouchers_tab, columns=columns, show="headings")
        
        for col in columns:
            self.voucher_tree.heading(col, text=col)
            self.voucher_tree.column(col, width=100)
            
        self.voucher_tree.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.view_vouchers_tab, orient=tk.VERTICAL, 
                                command=self.voucher_tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.voucher_tree.configure(yscrollcommand=scrollbar.set)
        
        # Refresh button
        ttk.Button(self.view_vouchers_tab, text="Refresh", 
                  command=self.refresh_vouchers).grid(row=1, column=0, pady=5)
        
    def create_voucher(self):
        code = self.voucher_code.get()
        discount = self.discount_amount.get()
        expiry = self.expiry_date.get()
        
        if not all([code, discount, expiry]):
            messagebox.showerror("Error", "All fields are required!")
            return
            
        try:
            discount = float(discount)
            expiry_date = datetime.strptime(expiry, "%Y-%m-%d")
        except ValueError:
            messagebox.showerror("Error", "Invalid discount amount or date format!")
            return
            
        voucher = {
            "code": code,
            "discount": discount,
            "expiry": expiry,
            "status": "Active"
        }
        
        self.vouchers.append(voucher)
        self.save_vouchers()
        self.refresh_vouchers()
        
        messagebox.showinfo("Success", "Voucher created successfully!")
        self.clear_create_form()
        
    def clear_create_form(self):
        self.voucher_code.delete(0, tk.END)
        self.discount_amount.delete(0, tk.END)
        self.expiry_date.delete(0, tk.END)
        
    def refresh_vouchers(self):
        # Clear existing items
        for item in self.voucher_tree.get_children():
            self.voucher_tree.delete(item)
            
        # Add vouchers to treeview
        for voucher in self.vouchers:
            self.voucher_tree.insert("", tk.END, values=(
                voucher["code"],
                f"${voucher['discount']}",
                voucher["expiry"],
                voucher["status"]
            ))
            
    def load_vouchers(self):
        try:
            if os.path.exists("vouchers.json"):
                with open("vouchers.json", "r") as f:
                    self.vouchers = json.load(f)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load vouchers: {str(e)}")
            
    def save_vouchers(self):
        try:
            with open("vouchers.json", "w") as f:
                json.dump(self.vouchers, f)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save vouchers: {str(e)}")

def main():
    root = tk.Tk()
    app = VoucherManagementSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main() 