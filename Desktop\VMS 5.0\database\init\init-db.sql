-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS vms_production;

-- Use the database
USE vms_production;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(36) PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'manager', 'operator', 'viewer', 'USER') NOT NULL,
  department VARCHAR(50) NOT NULL,
  date_created DATETIME NOT NULL,
  last_login DATETIME,
  is_active BOOLEAN DEFAULT TRUE,
  email VARCHAR(255)
);

-- Create vouchers table
CREATE TABLE IF NOT EXISTS vouchers (
  id VARCHAR(36) PRIMARY KEY,
  voucher_id VARCHAR(50) NOT NULL UNIQUE,
  date VARCHAR(50) NOT NULL,
  claimant VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  amount DECIMAL(15, 2) NOT NULL,
  currency ENUM('GHS', 'USD', 'GBP', 'EUR') NOT NULL,
  department VARCHAR(50) NOT NULL,
  dispatched_by VARCHAR(255),
  dispatch_time VARCHAR(50),
  status VARCHAR(50) NOT NULL,
  sent_to_audit BOOLEAN DEFAULT FALSE,
  created_by VARCHAR(255) NOT NULL,
  batch_id VARCHAR(36),
  received_by VARCHAR(255),
  receipt_time VARCHAR(50),
  comment TEXT,
  tax_type VARCHAR(50),
  tax_details TEXT,
  tax_amount DECIMAL(15, 2),
  pre_audited_amount DECIMAL(15, 2),
  pre_audited_by VARCHAR(255),
  certified_by VARCHAR(255),
  certified BOOLEAN DEFAULT FALSE,
  audit_dispatch_time VARCHAR(50),
  audit_dispatched_by VARCHAR(255),
  dispatch_to_on_department BOOLEAN DEFAULT FALSE,
  post_provisional_cash BOOLEAN DEFAULT FALSE,
  dispatched BOOLEAN DEFAULT FALSE,
  dispatch_to_audit_by VARCHAR(255),
  is_returned BOOLEAN DEFAULT FALSE,
  return_comment TEXT,
  return_time VARCHAR(50),
  deleted BOOLEAN DEFAULT FALSE,
  deletion_time VARCHAR(50),
  rejection_time VARCHAR(50),
  department_receipt_time VARCHAR(50),
  department_received_by VARCHAR(255),
  department_rejected BOOLEAN DEFAULT FALSE,
  rejected_by VARCHAR(255),
  pending_return BOOLEAN DEFAULT FALSE,
  return_initiated_time VARCHAR(50),
  reference_id VARCHAR(50)
);

-- Create voucher_batches table
CREATE TABLE IF NOT EXISTS voucher_batches (
  id VARCHAR(36) PRIMARY KEY,
  department VARCHAR(50) NOT NULL,
  sent_by VARCHAR(255) NOT NULL,
  sent_time VARCHAR(50) NOT NULL,
  received BOOLEAN DEFAULT FALSE,
  from_audit BOOLEAN DEFAULT FALSE
);

-- Create batch_vouchers table (junction table for many-to-many relationship)
CREATE TABLE IF NOT EXISTS batch_vouchers (
  batch_id VARCHAR(36) NOT NULL,
  voucher_id VARCHAR(36) NOT NULL,
  PRIMARY KEY (batch_id, voucher_id),
  FOREIGN KEY (batch_id) REFERENCES voucher_batches(id) ON DELETE CASCADE,
  FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE
);

-- Create provisional_cash_records table
CREATE TABLE IF NOT EXISTS provisional_cash_records (
  id VARCHAR(36) PRIMARY KEY,
  voucher_id VARCHAR(36) NOT NULL,
  voucher_ref VARCHAR(50) NOT NULL,
  claimant VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  main_amount DECIMAL(15, 2) NOT NULL,
  currency ENUM('GHS', 'USD', 'GBP', 'EUR') NOT NULL,
  amount_retired DECIMAL(15, 2),
  clearance_remark ENUM('CLEARED', 'REFUNDED TO CHEST', 'DUE STAFF', 'RETURNED'),
  date_retired VARCHAR(50),
  cleared_by VARCHAR(255),
  comment TEXT,
  date VARCHAR(50) NOT NULL,
  FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  timestamp VARCHAR(50) NOT NULL,
  voucher_id VARCHAR(36),
  batch_id VARCHAR(36),
  type VARCHAR(50) NOT NULL,
  from_audit BOOLEAN DEFAULT FALSE
);

-- Create blacklisted_voucher_ids table
CREATE TABLE IF NOT EXISTS blacklisted_voucher_ids (
  id VARCHAR(36) PRIMARY KEY,
  voucher_id VARCHAR(50) NOT NULL UNIQUE
);

-- Create pending_registrations table
CREATE TABLE IF NOT EXISTS pending_registrations (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  password VARCHAR(255) NOT NULL,
  department VARCHAR(50) NOT NULL,
  date_requested VARCHAR(50) NOT NULL,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'
);

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  fiscal_year_start ENUM('JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC') DEFAULT 'JAN',
  fiscal_year_end ENUM('JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC') DEFAULT 'DEC',
  current_fiscal_year INT NOT NULL,
  system_time VARCHAR(50) NOT NULL,
  auto_backup_enabled BOOLEAN DEFAULT TRUE,
  session_timeout INT DEFAULT 30,
  last_backup_date VARCHAR(50)
);

-- Create resource_locks table for concurrency management
CREATE TABLE IF NOT EXISTS resource_locks (
  id VARCHAR(36) PRIMARY KEY,
  resource_type VARCHAR(50) NOT NULL,
  resource_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  department VARCHAR(50) NOT NULL,
  lock_time DATETIME NOT NULL,
  expiry_time DATETIME NOT NULL,
  UNIQUE KEY unique_resource (resource_type, resource_id)
);

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  action VARCHAR(255) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id VARCHAR(36),
  details TEXT,
  timestamp DATETIME NOT NULL,
  ip_address VARCHAR(50)
);

-- Create initial admin user (password: admin123)
INSERT INTO users (id, name, password, role, department, date_created, is_active)
VALUES (
  'admin-default',
  'System Administrator',
  '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf/U5OqOvdz3uyFwg0Pzv0K', -- hashed 'admin123'
  'ADMIN',
  'SYSTEM ADMIN',
  NOW(),
  TRUE
) ON DUPLICATE KEY UPDATE id = id;

-- Create initial system settings
INSERT INTO system_settings (fiscal_year_start, fiscal_year_end, current_fiscal_year, system_time, auto_backup_enabled, session_timeout)
VALUES (
  'JAN',
  'DEC',
  YEAR(CURDATE()),
  NOW(),
  TRUE,
  30
) ON DUPLICATE KEY UPDATE id = id;
