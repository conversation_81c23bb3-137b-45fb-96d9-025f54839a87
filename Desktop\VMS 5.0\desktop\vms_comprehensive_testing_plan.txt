# VMS COMPREHENSIVE TESTING PLAN

## TESTING APPROACH
1. Test each department's interface and functionality separately
2. Test the Audit department interface and functionality
3. Test the complete voucher lifecycle through the actual UI
4. Document all UI elements, buttons, text fields, and tables
5. Verify all error messages and validation
6. Test edge cases and exceptional flows

## DEPARTMENTS TO TEST
1. FINANCE
2. MINISTRIES
3. PENSIONS
4. PENTMEDIA
5. MISSIONS
6. PENTSOS
7. AUDIT

## TEST SCENARIOS

### 1. DEPARTMENT DASHBOARD AND VOUCHER CREATION

#### 1.1 Login and Dashboard
- Login to department account
- Verify dashboard layout and elements
- Check all tabs (PENDING, PROCESSING, CERTIFIED, REJECTED, RETURNED)
- Verify voucher counts in each tab
- Test navigation between tabs

#### 1.2 Voucher Creation Form
- Click "Create Voucher" button
- Verify all form fields:
  * Claimant
  * Description
  * Amount
  * Currency
  * Tax Type (if applicable)
  * Tax Details (if applicable)
  * Tax Amount (if applicable)
  * Comment (if applicable)
- Test form validation:
  * Empty required fields
  * Invalid amount format
  * Invalid tax amount
- Submit valid voucher
- Verify voucher appears in PENDING tab
- Verify voucher ID format and uniqueness
- Check voucher details in the table

#### 1.3 Voucher Table in PENDING Tab
- Verify table columns:
  * Voucher ID
  * Date
  * Claimant
  * Description
  * Amount
  * Currency
  * Actions
- Test sorting functionality (if available)
- Test filtering functionality (if available)
- Test pagination (if available)
- Verify voucher selection (checkboxes)

#### 1.4 Sending Vouchers to Audit
- Select vouchers using checkboxes
- Click "Send to Audit" button
- Verify dispatcher field
- Submit with empty dispatcher field (should show error)
- Submit with valid dispatcher
- Verify vouchers disappear from PENDING tab
- Verify vouchers appear in PROCESSING tab

### 2. AUDIT DEPARTMENT INTERFACE

#### 2.1 Login and Dashboard
- Login to Audit account
- Verify dashboard layout and elements
- Check all tabs and sections
- Verify notification for new vouchers
- Test navigation between sections

#### 2.2 Receiving Vouchers
- Navigate to "Voucher Hub" or equivalent
- Verify table of pending vouchers
- Test voucher selection
- Click "Receive" button
- Verify vouchers move to processing queue

#### 2.3 Processing Vouchers
- Select voucher for processing
- Verify voucher details display
- Test pre-audit functionality:
  * Enter pre-audit amount
  * Add comments
  * Submit pre-audit
- Test certification:
  * Click "Certify" button
  * Verify confirmation dialog
  * Confirm certification
  * Verify voucher status changes
- Test rejection:
  * Click "Reject" button
  * Enter rejection reason
  * Submit rejection
  * Verify voucher status changes
- Test return:
  * Click "Return" button
  * Enter return reason
  * Submit return
  * Verify voucher status changes

#### 2.4 Dispatching Vouchers
- Navigate to "Pending Dispatch" tab
- Verify table of processed vouchers
- Test voucher selection
- Enter dispatcher name
- Click "Send to Department" button
- Verify vouchers move to "Dispatched" tab

### 3. DEPARTMENT RECEIVING VOUCHERS

#### 3.1 Notifications
- Verify notification appears for returned vouchers
- Click on notification
- Verify navigation to appropriate section

#### 3.2 Receiving Vouchers
- Navigate to "Receive Vouchers" section
- Verify table of vouchers to receive
- Test batch selection
- Click "Receive" button
- Verify vouchers appear in appropriate tabs

#### 3.3 Viewing Certified Vouchers
- Navigate to CERTIFIED tab
- Verify table of certified vouchers
- Click on voucher to view details
- Verify all voucher details are displayed correctly

#### 3.4 Handling Rejected Vouchers
- Navigate to REJECTED tab
- Verify table of rejected vouchers
- Click on voucher to view details
- Verify rejection reason is displayed
- Test "Delete" functionality (if available)
- Test "Re-add" functionality:
  * Click "Add Back" button
  * Verify new voucher creation
  * Verify new voucher appears in PENDING tab

#### 3.5 Handling Returned Vouchers
- Navigate to RETURNED tab
- Verify table of returned vouchers
- Click on voucher to view details
- Verify return reason is displayed
- Test "Delete" functionality (if available)
- Test "Re-add" functionality:
  * Click "Add Back" button
  * Verify new voucher creation
  * Verify new voucher appears in PENDING tab

### 4. EDGE CASES AND EXCEPTIONAL FLOWS

#### 4.1 Multiple Vouchers
- Create multiple vouchers in quick succession
- Verify all vouchers have unique IDs
- Send multiple vouchers to Audit
- Process multiple vouchers in Audit
- Dispatch multiple vouchers from Audit
- Receive multiple vouchers in department

#### 4.2 Concurrent Access
- Access same department from multiple browsers
- Verify changes in one browser reflect in others
- Test concurrent voucher creation
- Test concurrent voucher processing

#### 4.3 Error Handling
- Test network disconnection during voucher submission
- Test session timeout during processing
- Verify error messages are clear and helpful
- Test recovery from errors

## EXPECTED RESULTS
For each test, document:
- UI elements present
- Button functionality
- Text field validation
- Table content and behavior
- Error messages
- Workflow transitions
- Any discrepancies or issues found
