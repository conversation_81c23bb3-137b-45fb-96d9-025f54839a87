# VMS VOUCHER MOVEMENT ALGORITHM TESTING PLAN

## TESTING APPROACH
1. Test each step of the voucher movement algorithm sequentially
2. Test for each department (FINANCE, MINISTRIES, PENSIONS, PENTMEDIA, MISSIONS, PENTSOS)
3. Document expected behavior and actual results
4. Fix any issues encountered
5. Verify fixes with additional testing

## TEST SCENARIOS

### 1. DEPARTMENT TO AUDIT FLOW

#### 1.1 Voucher Creation
- Create voucher in each department
- Verify voucher ID generation
- Verify voucher appears in PENDING tab
- Verify initial status is "PENDING SUBMISSION"

#### 1.2 Sending Voucher to Audit
- Select voucher from PENDING tab
- Send to Audit
- Verify voucher moves to PROCESSING tab
- Verify status remains "PENDING SUBMISSION"
- Verify sent_to_audit flag is set to true

#### 1.3 Audit Receiving Vouchers
- Login as Audit
- Verify notification received
- Receive vouchers
- Verify status changes to "AUDIT: PROCESSING"
- Verify vouchers appear in Audit processing queue

#### 1.4 Audit Processing
- Test pre-auditing (set amount and comments)
- Test certification
- Test rejection
- Test return
- Verify appropriate status changes and field updates

#### 1.5 Audit Dispatching Vouchers
- Select processed vouchers
- Dispatch back to department
- Verify vouchers move to Dispatched tab
- Verify appropriate fields are updated

#### 1.6 Department Receiving Vouchers
- Login as department
- Verify notification received
- Receive vouchers
- Verify vouchers appear in appropriate tabs (CERTIFIED, REJECTED, RETURNED)
- Verify appropriate fields are updated

#### 1.7 Department Post-Receipt Actions
- Test viewing certified vouchers
- Test re-adding rejected vouchers
- Test re-adding returned vouchers
- Verify new vouchers appear in PENDING tab
- Verify reference to original voucher

### 2. AUDIT TO DEPARTMENT FLOW (ADDITIONAL TESTS)

#### 2.1 Audit-Initiated Vouchers
- Test if Audit can create vouchers
- Verify appropriate behavior

#### 2.2 Audit Special Processing
- Test any Audit-specific workflows
- Verify appropriate behavior

## EDGE CASES TO TEST

1. Concurrent voucher creation
2. Multiple vouchers in batch processing
3. Session timeout during processing
4. Network disconnection during processing
5. Invalid status transitions
6. Permission boundary testing

## EXPECTED RESULTS
For each test, document:
- Expected behavior
- Actual behavior
- Any discrepancies
- Fixes implemented
- Verification of fixes
