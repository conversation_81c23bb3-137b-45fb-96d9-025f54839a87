# Use a Node.js image with a compatible architecture
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY server/package*.json ./

# Install dependencies without bcrypt
RUN npm uninstall bcrypt --no-save && npm ci --omit=dev

# Copy the server application
COPY server/ ./

# Create logs directory
RUN mkdir -p logs

# Create sessions table and other setup
COPY server/setup/ ./setup/

# Build TypeScript
RUN npm run build

# No need to clean up build dependencies since we're not using any

# Expose ports
EXPOSE 8080 8081

# Start the application
CMD ["node", "dist/index.js"]
