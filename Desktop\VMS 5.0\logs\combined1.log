{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:27:09"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:28:44"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:28 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:28"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 43756194-1748-42d1-a0d4-e17a0b28e391","service":"vms-server","timestamp":"2025-04-20 23:31:28"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 43756194-1748-42d1-a0d4-e17a0b28e391","service":"vms-server","timestamp":"2025-04-20 23:31:28"}
{"level":"info","message":"Found inactive session: {\"id\":\"43756194-1748-42d1-a0d4-e17a0b28e391\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:27:07.000Z\",\"last_activity\":\"2025-04-20T23:31:28.000Z\",\"session_end\":\"2025-04-20T23:31:28.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:31:28"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:28 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:28"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:29 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:29"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:29 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:29"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:29"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:34"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 2595bf9c-41d0-4101-a53c-8b54d2510e17, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:31:35 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Socket 4BXWsxRtfuhIhbD-AAAB is in rooms: 4BXWsxRtfuhIhbD-AAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:FINANCE-FINANCE","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:FINANCE-FINANCE","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:FINANCE-FINANCE by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting department-specific lock update to FINANCE: voucher-hub:FINANCE-FINANCE is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:FINANCE-FINANCE is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:MINISTRIES-MINISTRIES","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:MINISTRIES-MINISTRIES","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 0 connected users for department MINISTRIES","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:MINISTRIES-MINISTRIES by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting department-specific lock update to MINISTRIES: voucher-hub:MINISTRIES-MINISTRIES is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:MINISTRIES-MINISTRIES is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:PENSIONS-PENSIONS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:PENSIONS-PENSIONS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 0 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:PENSIONS-PENSIONS by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting department-specific lock update to PENSIONS: voucher-hub:PENSIONS-PENSIONS is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:PENSIONS-PENSIONS is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:PENTMEDIA-PENTMEDIA","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:PENTMEDIA-PENTMEDIA","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 0 connected users for department PENTMEDIA","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:PENTMEDIA-PENTMEDIA by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting department-specific lock update to PENTMEDIA: voucher-hub:PENTMEDIA-PENTMEDIA is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:PENTMEDIA-PENTMEDIA is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:MISSIONS-MISSIONS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:MISSIONS-MISSIONS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 0 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:MISSIONS-MISSIONS by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting department-specific lock update to MISSIONS: voucher-hub:MISSIONS-MISSIONS is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:MISSIONS-MISSIONS is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:PENTSOS-PENTSOS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:PENTSOS-PENTSOS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 0 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:PENTSOS-PENTSOS by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting department-specific lock update to PENTSOS: voucher-hub:PENTSOS-PENTSOS is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:PENTSOS-PENTSOS is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Connected clients after join: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:31:35"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-4BXWsxRtfuhIhbD-AAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', '4BXWsxRtfuhIhbD-AAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:31:35"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:31:37"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:02 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:02 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 2595bf9c-41d0-4101-a53c-8b54d2510e17","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 2595bf9c-41d0-4101-a53c-8b54d2510e17","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:02 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 2595bf9c-41d0-4101-a53c-8b54d2510e17","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"Found inactive session: {\"id\":\"2595bf9c-41d0-4101-a53c-8b54d2510e17\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:31:35.000Z\",\"last_activity\":\"2025-04-20T23:32:02.000Z\",\"session_end\":\"2025-04-20T23:32:02.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:02 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:03"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: b5380a87-25a5-4812-aeb8-3e7a3db0382d, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:08 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User connected: FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) from department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) joined personal room: user:81bdc1b0-beb9-46bd-a4ef-42c227bf3b69","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Created/updated session record for FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69)","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Socket ZJUpY4GlkDRrUEFhAAAB is in rooms: ZJUpY4GlkDRrUEFhAAAB, department:FINANCE, user:81bdc1b0-beb9-46bd-a4ef-42c227bf3b69","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) announcing presence in FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) explicitly joining department:FINANCE room","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Connected clients after join: FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Returning 1 users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Returning 0 active locks","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) explicitly joining department:FINANCE room","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Connected clients after join: FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) announcing presence in FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:32:08"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:32:10"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:24 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:24 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: b5380a87-25a5-4812-aeb8-3e7a3db0382d","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: b5380a87-25a5-4812-aeb8-3e7a3db0382d","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: b5380a87-25a5-4812-aeb8-3e7a3db0382d","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"Found inactive session: {\"id\":\"b5380a87-25a5-4812-aeb8-3e7a3db0382d\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:32:08.000Z\",\"last_activity\":\"2025-04-20T23:32:24.000Z\",\"session_end\":\"2025-04-20T23:32:24.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:24 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:24"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:25"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:31 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:31"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:36 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:36"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:41 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:41"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:46"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:51"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:32:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:32:56"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:34:03"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:34:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:34:20"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:34:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:34:46"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:34:50 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:34:50"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:34:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:34:56"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:35:01 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:35:01"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:35:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:35:06"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:35:22 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:35:22"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:35:27 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:35:27"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-20 23:35:28"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 3c389b3d-9a26-4882-ba42-b5d0da2fdda3, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:35:29 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Socket U3yxsf7hdtvmtP4uAAAB is in rooms: U3yxsf7hdtvmtP4uAAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:FINANCE-FINANCE","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:FINANCE-FINANCE","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:FINANCE-FINANCE by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting department-specific lock update to FINANCE: voucher-hub:FINANCE-FINANCE is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:FINANCE-FINANCE is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:MINISTRIES-MINISTRIES","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:MINISTRIES-MINISTRIES","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 0 connected users for department MINISTRIES","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:MINISTRIES-MINISTRIES by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting department-specific lock update to MINISTRIES: voucher-hub:MINISTRIES-MINISTRIES is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:MINISTRIES-MINISTRIES is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:PENSIONS-PENSIONS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:PENSIONS-PENSIONS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 0 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:PENSIONS-PENSIONS by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting department-specific lock update to PENSIONS: voucher-hub:PENSIONS-PENSIONS is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:PENSIONS-PENSIONS is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:PENTMEDIA-PENTMEDIA","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:PENTMEDIA-PENTMEDIA","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 0 connected users for department PENTMEDIA","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:PENTMEDIA-PENTMEDIA by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting department-specific lock update to PENTMEDIA: voucher-hub:PENTMEDIA-PENTMEDIA is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:PENTMEDIA-PENTMEDIA is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:MISSIONS-MISSIONS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:MISSIONS-MISSIONS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 0 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:MISSIONS-MISSIONS by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting department-specific lock update to MISSIONS: voucher-hub:MISSIONS-MISSIONS is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:MISSIONS-MISSIONS is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) is now viewing voucher-hub:PENTSOS-PENTSOS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 viewers for resource voucher-hub:PENTSOS-PENTSOS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 0 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Lock automatically acquired for voucher-hub:PENTSOS-PENTSOS by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting department-specific lock update to PENTSOS: voucher-hub:PENTSOS-PENTSOS is now locked by SAMUEL ASIEDU","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting lock update: voucher-hub:PENTSOS-PENTSOS is now locked by SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Connected clients after join: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:35:29"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-U3yxsf7hdtvmtP4uAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'U3yxsf7hdtvmtP4uAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:35:29"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:35:30"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:39:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:38 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:38"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:38 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:38"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 3c389b3d-9a26-4882-ba42-b5d0da2fdda3","service":"vms-server","timestamp":"2025-04-20 23:41:38"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 3c389b3d-9a26-4882-ba42-b5d0da2fdda3","service":"vms-server","timestamp":"2025-04-20 23:41:38"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:39"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 3c389b3d-9a26-4882-ba42-b5d0da2fdda3","service":"vms-server","timestamp":"2025-04-20 23:41:39"}
{"level":"info","message":"Found inactive session: {\"id\":\"3c389b3d-9a26-4882-ba42-b5d0da2fdda3\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:35:28.000Z\",\"last_activity\":\"2025-04-20T23:41:38.000Z\",\"session_end\":\"2025-04-20T23:41:38.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:41:39"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:39 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:39"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:41 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:41"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:46"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:51"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: b5cf48c7-bedb-4a62-8897-8272bd5a2816, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:56 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:41:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Socket fv7dJUlK1If0oKNBAAAB is in rooms: fv7dJUlK1If0oKNBAAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Connected clients after join: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:41:56"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-fv7dJUlK1If0oKNBAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'fv7dJUlK1If0oKNBAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:41:56"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:41:57"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:12 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:12 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: b5cf48c7-bedb-4a62-8897-8272bd5a2816","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: b5cf48c7-bedb-4a62-8897-8272bd5a2816","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:12 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: b5cf48c7-bedb-4a62-8897-8272bd5a2816","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"Found inactive session: {\"id\":\"b5cf48c7-bedb-4a62-8897-8272bd5a2816\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:41:56.000Z\",\"last_activity\":\"2025-04-20T23:45:12.000Z\",\"session_end\":\"2025-04-20T23:45:12.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:12 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:12"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:13"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:18"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: fa133b5c-3803-4276-8584-de2b9b725103, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:19 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User connected: FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) from department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) joined personal room: user:81bdc1b0-beb9-46bd-a4ef-42c227bf3b69","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Created/updated session record for FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69)","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Socket xbyFrV5IBuTInc0CAAAB is in rooms: xbyFrV5IBuTInc0CAAAB, department:FINANCE, user:81bdc1b0-beb9-46bd-a4ef-42c227bf3b69","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) announcing presence in FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) explicitly joining department:FINANCE room","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Connected clients after join: FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Returning 1 users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Returning 0 active locks","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) explicitly joining department:FINANCE room","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Connected clients after join: FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) announcing presence in FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:19 +0000] \"GET /api/users?_t=1745192719522 HTTP/1.1\" 200 641 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:19"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('81bdc1b0-beb9-46bd-a4ef-42c227bf3b69-xbyFrV5IBuTInc0CAAAB', '81bdc1b0-beb9-46bd-a4ef-42c227bf3b69', 'FELIX AYISI', 'FINANCE', 'xbyFrV5IBuTInc0CAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:45:19"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:45:23"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:39 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:39"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:39 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:39"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:39"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:40 +0000] \"GET /api/auth/me HTTP/1.1\" 200 142 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"User connected: FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) from department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) joined personal room: user:81bdc1b0-beb9-46bd-a4ef-42c227bf3b69","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Created/updated session record for FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69)","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Socket DFIHQ3-sZySljoh-AAAB is in rooms: DFIHQ3-sZySljoh-AAAB, department:FINANCE, user:81bdc1b0-beb9-46bd-a4ef-42c227bf3b69","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) explicitly joining department:FINANCE room","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Connected clients after join: FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) announcing presence in FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-04-20 23:45:40"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:45:47"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:49 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:49"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:45:54 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:45:54"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:46:12"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:04"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: fa133b5c-3803-4276-8584-de2b9b725103","service":"vms-server","timestamp":"2025-04-20 23:48:04"}
{"level":"info","message":"Found inactive session: {\"id\":\"fa133b5c-3803-4276-8584-de2b9b725103\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:45:19.000Z\",\"last_activity\":\"2025-04-20T23:45:39.000Z\",\"session_end\":\"2025-04-20T23:45:40.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:48:04"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:04 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:04"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:06 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:06"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:06 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:06"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:06"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:11 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:11"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:16 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:16"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:21 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:21"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:48:23"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:26 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:26"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:48:28"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:31 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:31"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:48:33"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:36 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:36"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:41 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:41"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:46"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:51"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:48:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:48:56"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:01 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:01"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:03 +0000] \"POST /api/auth/register HTTP/1.1\" 201 113 \"http://localhost/register\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:03"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:05"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:06"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:10 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:10"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:11 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:11"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:15 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:15"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:16 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:16"}
{"level":"info","message":"User System Administrator (admin-default) logged in successfully. Session ID: b7ec5d65-91a2-473d-970d-d6fa23aee784, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:17 +0000] \"POST /api/auth/login HTTP/1.1\" 200 526 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:17 +0000] \"GET /api/users/registrations/pending?_t=1745192957766 HTTP/1.1\" 200 409 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:17 +0000] \"GET /api/users?_t=1745192957766 HTTP/1.1\" 200 641 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"User connected: System Administrator (admin-default) from department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"User System Administrator (admin-default) joined room: department:SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"User System Administrator (admin-default) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"User System Administrator (admin-default) joined personal room: user:admin-default","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Broadcasting 1 connected users for department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Created/updated session record for System Administrator (admin-default)","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Total connected clients: 1, 1 in department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Socket ThXYzufiR-Tf5TS-AAAB is in rooms: ThXYzufiR-Tf5TS-AAAB, department:SYSTEM ADMIN, admin-users, user:admin-default","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"User System Administrator (admin-default) explicitly joining department:SYSTEM ADMIN room","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Connected clients after join: System Administrator (SYSTEM ADMIN)","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Broadcasting 1 connected users for department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"User System Administrator (admin-default) announcing presence in SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Broadcasting 1 connected users for department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:49:17.852Z by user System Administrator (SYSTEM ADMIN), query department: SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Fetching vouchers for specified department: SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:17 +0000] \"GET /api/users?_t=1745192957833 HTTP/1.1\" 200 641 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Found 0 vouchers for request from System Administrator (SYSTEM ADMIN)","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:17 +0000] \"GET /api/vouchers?department=SYSTEM+ADMIN&timestamp=1745192957833 HTTP/1.1\" 200 2 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:49:17.865Z by user System Administrator (SYSTEM ADMIN), query department: SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Fetching vouchers for specified department: SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"Found 0 vouchers for request from System Administrator (SYSTEM ADMIN)","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:17 +0000] \"GET /api/vouchers?department=SYSTEM+ADMIN&timestamp=1745192957853 HTTP/1.1\" 200 2 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:20 +0000] \"GET /api/users/registrations/pending?_t=1745192960348 HTTP/1.1\" 200 409 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:20"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:20"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:22 +0000] \"GET /api/users?_t=1745192962773 HTTP/1.1\" 200 641 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:22"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:22 +0000] \"GET /api/users/registrations/pending?_t=1745192962773 HTTP/1.1\" 200 409 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:22"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:25 +0000] \"GET /api/users/registrations/pending?_t=1745192965348 HTTP/1.1\" 200 409 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:25"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:25"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:27 +0000] \"GET /api/users?_t=1745192967767 HTTP/1.1\" 200 641 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:27"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:27 +0000] \"GET /api/users/registrations/pending?_t=1745192967767 HTTP/1.1\" 200 409 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:27"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:30 +0000] \"GET /api/users/registrations/pending?_t=1745192970364 HTTP/1.1\" 200 409 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:30"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:30 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 262 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:30"}
{"level":"info","message":"Broadcasting user update: created for user WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc)","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Broadcasting to 1 connected clients","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Emitting to department room: AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Broadcast complete for user WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc)","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"POST /api/users/registrations/e9f8f983-526b-4da1-9a26-3acb65dff802/approve HTTP/1.1\" 200 48 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"GET /api/users/registrations/pending?_t=1745192972486 HTTP/1.1\" 200 197 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"User System Administrator (admin-default) logged out successfully. Session ID: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"POST /api/auth/logout HTTP/1.1\" 200 37 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"warn","message":"User System Administrator (admin-default) attempted to access with an inactive session: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"GET /api/users?_t=1745192972486 HTTP/1.1\" - - \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Found inactive session: {\"id\":\"b7ec5d65-91a2-473d-970d-d6fa23aee784\",\"user_id\":\"admin-default\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:49:17.000Z\",\"last_activity\":\"2025-04-20T23:49:32.000Z\",\"session_end\":\"2025-04-20T23:49:32.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"User System Administrator (admin-default) logged out successfully. Session ID: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"GET /api/users/registrations/pending?_t=1745192972492 HTTP/1.1\" - - \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"User disconnected: System Administrator (admin-default) from department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"User System Administrator (admin-default) has no remaining connections","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Updated session record for System Administrator (admin-default) to inactive","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Broadcasting 0 connected users for department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department SYSTEM ADMIN","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"warn","message":"User System Administrator (admin-default) attempted to access with an inactive session: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Found inactive session: {\"id\":\"b7ec5d65-91a2-473d-970d-d6fa23aee784\",\"user_id\":\"admin-default\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:49:17.000Z\",\"last_activity\":\"2025-04-20T23:49:32.000Z\",\"session_end\":\"2025-04-20T23:49:32.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"warn","message":"User System Administrator (admin-default) attempted to access with an inactive session: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"warn","message":"User System Administrator (admin-default) attempted to access with an inactive session: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Found inactive session: {\"id\":\"b7ec5d65-91a2-473d-970d-d6fa23aee784\",\"user_id\":\"admin-default\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:49:17.000Z\",\"last_activity\":\"2025-04-20T23:49:32.000Z\",\"session_end\":\"2025-04-20T23:49:32.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"GET /api/users?_t=1745192972616 HTTP/1.1\" 401 86 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Found inactive session: {\"id\":\"b7ec5d65-91a2-473d-970d-d6fa23aee784\",\"user_id\":\"admin-default\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:49:17.000Z\",\"last_activity\":\"2025-04-20T23:49:32.000Z\",\"session_end\":\"2025-04-20T23:49:32.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"GET /api/users/registrations/pending?_t=1745192972615 HTTP/1.1\" 401 86 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"warn","message":"User System Administrator (admin-default) attempted to access with an inactive session: b7ec5d65-91a2-473d-970d-d6fa23aee784","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"Found inactive session: {\"id\":\"b7ec5d65-91a2-473d-970d-d6fa23aee784\",\"user_id\":\"admin-default\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:49:17.000Z\",\"last_activity\":\"2025-04-20T23:49:32.000Z\",\"session_end\":\"2025-04-20T23:49:32.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:32 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:35"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:36 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:36"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:36 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:36"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:37 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:37"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:37 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:37"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:37 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:37"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:37 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/admin-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:37"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:37 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:37"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:40 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:40"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:42 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:42"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:45 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:45"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:47 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:47"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged in successfully. Session ID: 41cecf53-066c-49de-b45f-1430ee7b48fa, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:49 +0000] \"POST /api/auth/login HTTP/1.1\" 200 552 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"User connected: WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined personal room: user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Created/updated session record for WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc)","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Socket 8yu038yJs5dAlitoAAAD is in rooms: 8yu038yJs5dAlitoAAAD, department:AUDIT, admin-users, user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Connected clients after join: WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:49:49"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:51"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:49:52.648Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:49:52"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:49:52"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:49:52"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:49:52"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:52 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745192992616 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:52"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:49:55"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:49:57.660Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:49:57"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:49:57"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:49:57"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:49:57"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:49:57 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745192997625 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:49:57"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:00 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:50:00"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:02.735Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:02"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:02"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:02"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:02 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193002708 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:50:05"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:07.739Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:07"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:07"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:07"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:07"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:07 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193007711 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:07"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:10 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:50:10"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-20 23:50:11"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: b3be19ba-8352-4a01-9aa9-1890c9316bb6, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:12 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"Socket JNsluT-5ONoqVglIAAAB is in rooms: JNsluT-5ONoqVglIAAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:50:12"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:13.795Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:13 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193012712 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:13"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:16.584Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:16"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:16"}
{"level":"info","message":"Found 1 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:16"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:16"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:16 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193016560 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:50:16"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:17.744Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:17"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:17"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:17"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:17"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:17 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193017717 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:17"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:22.748Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:22"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:22"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:22"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:22"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:22 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193022713 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:22"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:27.645Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:27"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:27"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:27"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:27"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:27 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193027621 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:27"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:35.633Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:35"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:35"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:35"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:35"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:35 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193035608 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:35"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:40.742Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:40"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:40"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:40"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:40"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:40 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193040707 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:40"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:50:45.644Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:45"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:50:45"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:50:45"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:50:45"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:50:45 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193045621 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:50:45"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:53:31"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:31 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:31"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: b3be19ba-8352-4a01-9aa9-1890c9316bb6","service":"vms-server","timestamp":"2025-04-20 23:56:32"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: b3be19ba-8352-4a01-9aa9-1890c9316bb6","service":"vms-server","timestamp":"2025-04-20 23:56:32"}
{"level":"info","message":"Found inactive session: {\"id\":\"b3be19ba-8352-4a01-9aa9-1890c9316bb6\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:50:12.000Z\",\"last_activity\":\"2025-04-20T23:56:31.000Z\",\"session_end\":\"2025-04-20T23:56:32.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:56:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:32 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:32"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:34 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:34 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:39"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 9bbd3925-5e7e-4a67-a3d1-8629816deb52, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:56:43 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Socket uQ-QYSX_J1u_sXXjAAAB is in rooms: uQ-QYSX_J1u_sXXjAAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Connected clients after join: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:56:43"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-uQ-QYSX_J1u_sXXjAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'uQ-QYSX_J1u_sXXjAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:56:43"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:56:44"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:34 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:34 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: 41cecf53-066c-49de-b45f-1430ee7b48fa","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: 41cecf53-066c-49de-b45f-1430ee7b48fa","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) attempted to access with an inactive session: 41cecf53-066c-49de-b45f-1430ee7b48fa","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"Found inactive session: {\"id\":\"41cecf53-066c-49de-b45f-1430ee7b48fa\",\"user_id\":\"e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:49:49.000Z\",\"last_activity\":\"2025-04-20T23:57:34.000Z\",\"session_end\":\"2025-04-20T23:57:34.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:34 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:34"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:35"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:40 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:40"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged in successfully. Session ID: 49c9cf8f-1094-488b-9e7e-f9538d53b52e, IP: **********","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:42 +0000] \"POST /api/auth/login HTTP/1.1\" 200 552 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"User connected: WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) from department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: admin-users","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined personal room: user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Created/updated session record for WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc)","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Socket R4xOS9tEBJ1xJi1xAAAB is in rooms: R4xOS9tEBJ1xJi1xAAAB, department:AUDIT, admin-users, user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Connected clients after join: WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-20 23:57:42"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc-R4xOS9tEBJ1xJi1xAAAB', 'e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc', 'WILLIAM AKUAMOAH', 'AUDIT', 'R4xOS9tEBJ1xJi1xAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:57:42"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-20 23:57:44"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:57:51.579Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:57:51"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:57:51"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:57:51"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:57:51"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:51 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193471537 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:51"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:57:56.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:57:56"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:57:56"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:57:56"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:57:56"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:57:56 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193476715 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:57:56"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:01.739Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:01"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:01"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:01"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:01"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:01 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193481707 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:01"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:02.635Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:02"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:02"}
{"level":"info","message":"Found 1 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:02"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:02"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:02 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193482604 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-20 23:58:02"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:06.740Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:06"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:06"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:06"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:06"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:06 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193486713 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:06"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:11.736Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:11"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:11"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:11"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:11"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:11 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193491709 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:11"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:16.737Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:16"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:16"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:16"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:16"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:16 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193496704 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:16"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:21.750Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:21"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:21"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:21"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:21"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:21 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193501713 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:21"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:26.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:26"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:26"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:26"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:26"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:26 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193506710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:26"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:31.732Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:31"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:31"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:31"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:31"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:31 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193511706 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:31"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:36.749Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:36"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:36"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:36"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:36"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:36 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193516717 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:36"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:41.748Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:41"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:41"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:41"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:41"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:41 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193521716 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:41"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:46.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:46"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:46"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:46"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:46"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:46 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193526720 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:46"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:58:51.753Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:51"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:58:51"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:58:51"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:58:51"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:58:51 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193531718 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:58:51"}
{"level":"info","message":"GET /vouchers request at 2025-04-20T23:59:20.738Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:59:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-20 23:59:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-20 23:59:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-20 23:59:20"}
{"level":"info","message":"::ffff:********** - - [20/Apr/2025:23:59:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193560711 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-20 23:59:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:00:20.729Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:00:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:00:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:00:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:00:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:00:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193620703 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:00:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:01:20.739Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:01:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:01:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:01:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:01:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:01:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193680709 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:01:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:02:20.737Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:02:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:02:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:02:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:02:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193740710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:43 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:43 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:43"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 9bbd3925-5e7e-4a67-a3d1-8629816deb52","service":"vms-server","timestamp":"2025-04-21 00:02:44"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 9bbd3925-5e7e-4a67-a3d1-8629816deb52","service":"vms-server","timestamp":"2025-04-21 00:02:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:44 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:44"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 9bbd3925-5e7e-4a67-a3d1-8629816deb52","service":"vms-server","timestamp":"2025-04-21 00:02:44"}
{"level":"info","message":"Found inactive session: {\"id\":\"9bbd3925-5e7e-4a67-a3d1-8629816deb52\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:56:43.000Z\",\"last_activity\":\"2025-04-21T00:02:43.000Z\",\"session_end\":\"2025-04-21T00:02:43.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:02:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:44 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:45 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:45"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:02:47.787Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:02:47"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:02:47"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:02:47"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:02:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:47 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193767761 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:50 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:50 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: 49c9cf8f-1094-488b-9e7e-f9538d53b52e","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: 49c9cf8f-1094-488b-9e7e-f9538d53b52e","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:50 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) attempted to access with an inactive session: 49c9cf8f-1094-488b-9e7e-f9538d53b52e","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"Found inactive session: {\"id\":\"49c9cf8f-1094-488b-9e7e-f9538d53b52e\",\"user_id\":\"e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-20T23:57:42.000Z\",\"last_activity\":\"2025-04-21T00:02:50.000Z\",\"session_end\":\"2025-04-21T00:02:50.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:50 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:02:56"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 2fc7ef1f-3f9f-41e3-98b4-d10439cf73d4, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:02:58 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Socket F88gCEvEZrQ23rerAAAB is in rooms: F88gCEvEZrQ23rerAAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Connected clients after join: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:02:58"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:03:00"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:01 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:01"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:05"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:10 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:10"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged in successfully. Session ID: d043357a-4208-4a2a-9f2d-e740013e1bb0, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:15 +0000] \"POST /api/auth/login HTTP/1.1\" 200 552 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"User connected: WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) from department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: admin-users","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined personal room: user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Created/updated session record for WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc)","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Socket ij1hS4iYd4k0WtY_AAAB is in rooms: ij1hS4iYd4k0WtY_AAAB, department:AUDIT, admin-users, user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Connected clients after join: WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:03:15"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc-ij1hS4iYd4k0WtY_AAAB', 'e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc', 'WILLIAM AKUAMOAH', 'AUDIT', 'ij1hS4iYd4k0WtY_AAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-21 00:03:15"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 00:03:16"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 00:03:16"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 00:03:17"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-21 00:03:17"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-21 00:03:17"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-21 00:03:17"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:03:17"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:03:17"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:27.064Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:27"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:27"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:27"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:27 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193807029 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:27"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:32.744Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:32"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:32"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:32"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:32"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:32 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193812714 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:32"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:37.080Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:37"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:37"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:37"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:37"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:37 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193817045 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:37"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:42.740Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:42"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:42"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:42"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:42"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:42 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193822709 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:42"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:47.443Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"Found 1 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:47 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193827420 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:47.733Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:47 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193827705 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:47"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:52.736Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:52"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:52"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:52"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:52"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:52 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193832705 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:52"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:03:57.747Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:57"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:03:57"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:03:57"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:03:57"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:03:57 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193837716 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:03:57"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:02.766Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:02"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:02"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:02"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:02 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193842710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:02"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:07.751Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:07"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:07"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:07"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:07 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193847714 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:07"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:12.751Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:12"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:12"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:12"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:12"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:12 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193852716 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:12"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:17.742Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:17"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:17"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:17"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:17"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:17 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193857711 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:17"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:22.737Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:22"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:22"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:22"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:22 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193862706 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:22"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:27.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:27"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:27"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:27"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:27 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193867712 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:27"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:32.742Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:32"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:32"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:32"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:32"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:32 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193872710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:32"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:04:37.744Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:37"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:04:37"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:04:37"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:04:37"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:04:37 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193877710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:04:37"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:05:20.747Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:05:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:05:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:05:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:05:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:05:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193920724 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:05:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:06:20.757Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:06:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:06:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:06:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:06:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:06:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745193980727 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:06:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:07:07.707Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:07:07"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:07:07"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:07:07"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:07:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:07 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194027680 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:14 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:14 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: d043357a-4208-4a2a-9f2d-e740013e1bb0","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: d043357a-4208-4a2a-9f2d-e740013e1bb0","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:14 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) attempted to access with an inactive session: d043357a-4208-4a2a-9f2d-e740013e1bb0","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"Found inactive session: {\"id\":\"d043357a-4208-4a2a-9f2d-e740013e1bb0\",\"user_id\":\"e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:03:15.000Z\",\"last_activity\":\"2025-04-21T00:07:14.000Z\",\"session_end\":\"2025-04-21T00:07:14.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:14 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:16 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:16"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:24 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:24 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 2fc7ef1f-3f9f-41e3-98b4-d10439cf73d4","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 2fc7ef1f-3f9f-41e3-98b4-d10439cf73d4","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 2fc7ef1f-3f9f-41e3-98b4-d10439cf73d4","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"Found inactive session: {\"id\":\"2fc7ef1f-3f9f-41e3-98b4-d10439cf73d4\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:02:58.000Z\",\"last_activity\":\"2025-04-21T00:07:24.000Z\",\"session_end\":\"2025-04-21T00:07:24.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:24 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:26 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:26"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:27 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:32 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:32"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:34"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 71cf1e4c-dec4-41ab-9c48-b288dec1c5f7, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:35 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User connected: SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) from department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined room: admin-users","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) joined personal room: user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Created/updated session record for SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01)","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Socket AfuXyiPcQ38IrAN-AAAB is in rooms: AfuXyiPcQ38IrAN-AAAB, department:AUDIT, admin-users, user:21d61814-cd59-4777-a0a0-0fdd9b085c01","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Connected clients after join: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:35"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:07:37"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:43"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged in successfully. Session ID: 468bb4f3-2164-4911-a716-da1e78ce807d, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 552 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"User connected: WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) from department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined room: admin-users","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) joined personal room: user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Created/updated session record for WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc)","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Socket VAsrRn6p1d1ukOJSAAAB is in rooms: VAsrRn6p1d1ukOJSAAAB, department:AUDIT, admin-users, user:e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Connected clients after join: WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) announcing presence in AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-04-21 00:07:47"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:07:48"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:07:51.767Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:07:51"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:07:51"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:07:51"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:07:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:51 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194071733 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:51"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:07:57.745Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:07:57"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:07:57"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:07:57"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:07:57"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:07:57 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194077713 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:07:57"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:02.740Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:02"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:02"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:02"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:02 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194082717 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:02"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:06.906Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:06"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:06"}
{"level":"info","message":"Found 1 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:06"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:06 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194086882 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:08:06"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:07.731Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:07"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:07"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:07"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:07 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194087707 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:07"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:12.745Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:12"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:12"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:12"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:12"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:12 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194092714 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:12"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:16.768Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:16"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:16"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:16"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:16"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:16 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194096745 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:16"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:22.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:22"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:22"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:22"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:22 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194102709 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:22"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:27.737Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:27"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:27"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:27"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:27 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194107710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:27"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:32.756Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:32"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:32"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:32"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:32"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:32 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194112714 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:32"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:37.751Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:37"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:37"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:37"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:37"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:37 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194117708 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:37"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:42.742Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:42"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:42"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:42"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:42"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:42 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194122712 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:42"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:47.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:47"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:47"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:47"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:47 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194127708 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:47"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:08:52.737Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:52"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:08:52"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:08:52"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:08:52"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:08:52 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194132716 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:08:52"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"Database connection established","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"Server running on port 8080","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"WebSocket server running on port 8081","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"Session cleanup task scheduled","service":"vms-server","timestamp":"2025-04-21 00:10:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:10:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:10:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:10:58 +0000] \"GET /api/auth/me HTTP/1.1\" 200 145 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:10:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:09 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:09"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:14 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:19 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:34"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:38"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 2 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:11:38"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 172f078a-44b8-4c8b-905b-e78500a6bfe8, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:11:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:38 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:11:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:48 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:48"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:11:49"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged in successfully. Session ID: f9d79910-610f-44be-9c27-8498e255ad1b, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:11:49"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:49 +0000] \"POST /api/auth/login HTTP/1.1\" 200 552 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:49"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:11:53.260Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:11:53"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:11:53"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:11:53"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:11:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:53 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194313236 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:53"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:11:58.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:11:58"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:11:58"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:11:58"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:11:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:11:58 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194318712 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:11:58"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:03.738Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:03"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:03"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:03"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:03 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194323710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:03"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:07.429Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:07"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:07"}
{"level":"info","message":"Found 1 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:07"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:07 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194327396 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:12:07"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:08.743Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:08"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:08"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:08"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:08 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194328711 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:08"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:13.740Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:13"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:13"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:13"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:13 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194333710 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:13"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:18.738Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:18"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:18"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:18"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:18 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194338717 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:18"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:23.739Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:23"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:23"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:23"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:23 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194343714 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:23"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:28.741Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:28"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:28"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:28"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:28 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194348713 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:28"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:33.733Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:33"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:33"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:33"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:33 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194353708 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:33"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:38.741Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:38"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:38"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:38"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:38 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194358718 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:38"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:43.745Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:43"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:43"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:43"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:43 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194363715 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:43"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:48.730Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:48"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:48"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:48"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:48 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194368704 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:48"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:12:53.737Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:53"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:12:53"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:12:53"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:12:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:12:53 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194373708 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:12:53"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:13:20.733Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:13:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:13:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:13:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:13:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:13:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194400706 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:13:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:14:20.736Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:14:20"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:14:20"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:14:20"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:14:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:14:20 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194460709 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:14:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:12 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:12"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 172f078a-44b8-4c8b-905b-e78500a6bfe8","service":"vms-server","timestamp":"2025-04-21 00:15:12"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 172f078a-44b8-4c8b-905b-e78500a6bfe8","service":"vms-server","timestamp":"2025-04-21 00:15:12"}
{"level":"info","message":"Found inactive session: {\"id\":\"172f078a-44b8-4c8b-905b-e78500a6bfe8\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:11:38.000Z\",\"last_activity\":\"2025-04-21T00:15:12.000Z\",\"session_end\":\"2025-04-21T00:15:12.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:15:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:13 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:14 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:14 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:14 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:14"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:15:16.912Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:16"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:16"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:15:16"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:15:16"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:16 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194516869 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:16"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:15:18.270Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:18"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:18"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:15:18"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:15:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:18 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194518242 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:20 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:20"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: f9d79910-610f-44be-9c27-8498e255ad1b","service":"vms-server","timestamp":"2025-04-21 00:15:20"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) attempted to access with an inactive session: f9d79910-610f-44be-9c27-8498e255ad1b","service":"vms-server","timestamp":"2025-04-21 00:15:20"}
{"level":"info","message":"Found inactive session: {\"id\":\"f9d79910-610f-44be-9c27-8498e255ad1b\",\"user_id\":\"e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:11:49.000Z\",\"last_activity\":\"2025-04-21T00:15:20.000Z\",\"session_end\":\"2025-04-21T00:15:20.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:15:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:20 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:23 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:23 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:30 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:30"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:33"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 2 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:15:34"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 1817a245-6125-4cf9-8ce0-e513321a691b, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:15:34"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:34 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:15:34"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:35"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:39"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:15:42"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged in successfully. Session ID: aca0bf5b-2482-41a9-ac0a-a88ed028d25c, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:15:42"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:42 +0000] \"POST /api/auth/login HTTP/1.1\" 200 552 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:42"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:15:47.724Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:47"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:47"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:15:47"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:15:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:47 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194547702 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:47"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:15:52.734Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:52"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:52"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:15:52"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:15:52"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:52 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194552713 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:52"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:15:57.734Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:57"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:15:57"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:15:57"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:15:57"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:15:57 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194557707 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:15:57"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:16:02.745Z by user WILLIAM AKUAMOAH (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:16:02"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:16:02"}
{"level":"info","message":"Found 1 vouchers for request from WILLIAM AKUAMOAH (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:16:02"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-04-21 00:16:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:16:02 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745194562717 HTTP/1.1\" 200 955 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:16:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:44 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:44 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 1817a245-6125-4cf9-8ce0-e513321a691b","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 1817a245-6125-4cf9-8ce0-e513321a691b","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:44 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 1817a245-6125-4cf9-8ce0-e513321a691b","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"Found inactive session: {\"id\":\"1817a245-6125-4cf9-8ce0-e513321a691b\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:15:34.000Z\",\"last_activity\":\"2025-04-21T00:22:44.000Z\",\"session_end\":\"2025-04-21T00:22:44.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:44 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:47 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:52 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:52"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: 2deb6fcc-5094-436f-bfdc-66f4832b3417, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:22:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:55 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:22:55 +0000] \"GET /api/users?_t=1745194975073 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:22:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:23:25 +0000] \"GET /api/users?_t=1745195005705 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:23:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:23:55 +0000] \"GET /api/users?_t=1745195035707 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:23:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:24:25 +0000] \"GET /api/users?_t=1745195065706 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:24:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:24:55 +0000] \"GET /api/users?_t=1745195095705 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:24:55"}
{"level":"info","message":"Cleaned up 1 stale sessions due to inactivity","service":"vms-server","timestamp":"2025-04-21 00:25:03"}
{"level":"info","message":"Expired session: 6a9bd300-726f-4132-b3e2-1ec1d61e0c49 for user 21d61814-cd59-4777-a0a0-0fdd9b085c01, inactive for 60 minutes","service":"vms-server","timestamp":"2025-04-21 00:25:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:25:25 +0000] \"GET /api/users?_t=1745195125714 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:25:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:26:00 +0000] \"GET /api/users?_t=1745195160931 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:26:00"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:26:25 +0000] \"GET /api/users?_t=1745195185719 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:26:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:26:55 +0000] \"GET /api/users?_t=1745195215718 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:26:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:27:56 +0000] \"GET /api/users?_t=1745195276708 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:27:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:28:57 +0000] \"GET /api/users?_t=1745195337716 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:28:57"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:29:28 +0000] \"GET /api/users?_t=1745195368214 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:29:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:29:55 +0000] \"GET /api/users?_t=1745195395707 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:29:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:30:25 +0000] \"GET /api/users?_t=1745195425704 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:30:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:30:55 +0000] \"GET /api/users?_t=1745195455717 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:30:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:34 +0000] \"GET /api/users?_t=1745195494047 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:34"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:38 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:38 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 2deb6fcc-5094-436f-bfdc-66f4832b3417","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 2deb6fcc-5094-436f-bfdc-66f4832b3417","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: 2deb6fcc-5094-436f-bfdc-66f4832b3417","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"Found inactive session: {\"id\":\"2deb6fcc-5094-436f-bfdc-66f4832b3417\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:22:55.000Z\",\"last_activity\":\"2025-04-21T00:31:38.000Z\",\"session_end\":\"2025-04-21T00:31:38.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:38 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:40 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:40"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:45 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:45"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: 9fdf64b1-5a63-472a-b7a0-d29d2162af5a, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:31:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:46 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:31:46 +0000] \"GET /api/users?_t=1745195506748 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:31:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:32:17 +0000] \"GET /api/users?_t=1745195537711 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:32:17"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:32:47 +0000] \"GET /api/users?_t=1745195567707 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:32:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:17 +0000] \"GET /api/users?_t=1745195597710 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:17"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:47 +0000] \"GET /api/users?_t=1745195627712 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:51 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:51"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 9fdf64b1-5a63-472a-b7a0-d29d2162af5a","service":"vms-server","timestamp":"2025-04-21 00:33:51"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: 9fdf64b1-5a63-472a-b7a0-d29d2162af5a","service":"vms-server","timestamp":"2025-04-21 00:33:51"}
{"level":"info","message":"Found inactive session: {\"id\":\"9fdf64b1-5a63-472a-b7a0-d29d2162af5a\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:31:46.000Z\",\"last_activity\":\"2025-04-21T00:33:51.000Z\",\"session_end\":\"2025-04-21T00:33:51.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:33:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:51 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:55 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:55 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" - - \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:33:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:33:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:34:00 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:34:00"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:34:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:34:05"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: 45346ee2-1b06-4bda-9ceb-c6669ec5deb6, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:34:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:34:08 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:34:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:34:08 +0000] \"GET /api/users?_t=1745195648439 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:34:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:34:38 +0000] \"GET /api/users?_t=1745195678717 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:34:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:35:08 +0000] \"GET /api/users?_t=1745195708718 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:35:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:35:38 +0000] \"GET /api/users?_t=1745195738712 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:35:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:36:08 +0000] \"GET /api/users?_t=1745195768713 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:36:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:36:38 +0000] \"GET /api/users?_t=1745195798715 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:36:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:37:20 +0000] \"GET /api/users?_t=1745195840718 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:37:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:38:20 +0000] \"GET /api/users?_t=1745195900711 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:38:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:39:20 +0000] \"GET /api/users?_t=1745195960706 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:39:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:40:20 +0000] \"GET /api/users?_t=1745196020704 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:40:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:41:20 +0000] \"GET /api/users?_t=1745196080712 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:41:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:20 +0000] \"GET /api/users?_t=1745196140704 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:42:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:21 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:21 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: aca0bf5b-2482-41a9-ac0a-a88ed028d25c","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) logged out successfully. Session ID: aca0bf5b-2482-41a9-ac0a-a88ed028d25c","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:21 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"warn","message":"User WILLIAM AKUAMOAH (e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc) attempted to access with an inactive session: aca0bf5b-2482-41a9-ac0a-a88ed028d25c","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"Found inactive session: {\"id\":\"aca0bf5b-2482-41a9-ac0a-a88ed028d25c\",\"user_id\":\"e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:15:42.000Z\",\"last_activity\":\"2025-04-21T00:42:21.000Z\",\"session_end\":\"2025-04-21T00:42:21.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:21 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:42:21"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:22 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:42:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:28 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:28 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 45346ee2-1b06-4bda-9ceb-c6669ec5deb6","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 45346ee2-1b06-4bda-9ceb-c6669ec5deb6","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: 45346ee2-1b06-4bda-9ceb-c6669ec5deb6","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"Found inactive session: {\"id\":\"45346ee2-1b06-4bda-9ceb-c6669ec5deb6\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:34:08.000Z\",\"last_activity\":\"2025-04-21T00:42:28.000Z\",\"session_end\":\"2025-04-21T00:42:28.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:28 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:42:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:42:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:42:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:43:36 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:43:36"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:43:41 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:43:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:43:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:43:46"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 00:43:46"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 22aa9386-3044-4724-8dd7-53035c8bed92, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:43:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:43:46 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:43:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:02 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:08"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: 9bccee71-a456-4e5d-a807-f6dedb12a25b, IP: **********","service":"vms-server","timestamp":"2025-04-21 00:44:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:11 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:11 +0000] \"GET /api/users?_t=1745196251438 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:11"}
{"level":"info","message":"Generated voucher ID: APR00032 for department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Created voucher with ID: 37d4a655-1d18-41c3-a1e3-67391dd3f4b4, Voucher ID: APR00032","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcasting voucher update: created for voucher APR00032 in department FINANCE","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcasting to 0 connected clients","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Found 0 clients in department FINANCE or with admin access","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Emitting to department room: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcasting created for voucher: 37d4a655-1d18-41c3-a1e3-67391dd3f4b4","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcasting to 0 connected clients","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcast complete for voucher 37d4a655-1d18-41c3-a1e3-67391dd3f4b4","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcast complete for voucher APR00032","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcasting created for voucher: 37d4a655-1d18-41c3-a1e3-67391dd3f4b4","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcasting to 0 connected clients","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"Broadcast complete for voucher 37d4a655-1d18-41c3-a1e3-67391dd3f4b4","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:38 +0000] \"POST /api/vouchers HTTP/1.1\" 201 949 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:41 +0000] \"GET /api/users?_t=1745196281449 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:41"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745196293897', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_00-44-53', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '37d4a655-1d18-41c3-a1e3-67391dd3f4b4'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 00:44:53"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745196293897', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_00-44-53', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '37d4a655-1d18-41c3-a1e3-67391dd3f4b4'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 00:44:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:44:53 +0000] \"PUT /api/vouchers/37d4a655-1d18-41c3-a1e3-67391dd3f4b4 HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:44:53"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T00:45:11.140Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"info","message":"Found 14 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:45:11 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745196311113 HTTP/1.1\" 200 15119 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:45:11 +0000] \"GET /api/users?_t=1745196311453 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:45:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:45:41 +0000] \"GET /api/users?_t=1745196341452 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:45:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:46:11 +0000] \"GET /api/users?_t=1745196371717 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:46:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:46:41 +0000] \"GET /api/users?_t=1745196401710 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:46:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:47:20 +0000] \"GET /api/users?_t=1745196440704 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:47:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:47:41 +0000] \"GET /api/users?_t=1745196461455 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:47:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:48:11 +0000] \"GET /api/users?_t=1745196491708 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:48:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:48:41 +0000] \"GET /api/users?_t=1745196521713 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:48:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:49:20 +0000] \"GET /api/users?_t=1745196560708 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:49:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:50:20 +0000] \"GET /api/users?_t=1745196620713 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:50:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:51:20 +0000] \"GET /api/users?_t=1745196680710 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:51:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:52:07 +0000] \"GET /api/users?_t=1745196727802 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:52:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:52:11 +0000] \"GET /api/users?_t=1745196731448 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:52:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:52:41 +0000] \"GET /api/users?_t=1745196761708 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:52:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:53:11 +0000] \"GET /api/users?_t=1745196791717 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:53:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:54:12 +0000] \"GET /api/users?_t=1745196852710 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:54:12"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:55:13 +0000] \"GET /api/users?_t=1745196913716 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:55:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:00 +0000] \"GET /api/users?_t=1745196959907 HTTP/1.1\" 200 860 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:00"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:04 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:04 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 9bccee71-a456-4e5d-a807-f6dedb12a25b","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 9bccee71-a456-4e5d-a807-f6dedb12a25b","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: 9bccee71-a456-4e5d-a807-f6dedb12a25b","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"Found inactive session: {\"id\":\"9bccee71-a456-4e5d-a807-f6dedb12a25b\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:44:11.000Z\",\"last_activity\":\"2025-04-21T00:56:04.000Z\",\"session_end\":\"2025-04-21T00:56:04.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:04 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:05"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:06 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:06 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 22aa9386-3044-4724-8dd7-53035c8bed92","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 22aa9386-3044-4724-8dd7-53035c8bed92","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 22aa9386-3044-4724-8dd7-53035c8bed92","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"Found inactive session: {\"id\":\"22aa9386-3044-4724-8dd7-53035c8bed92\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T00:43:46.000Z\",\"last_activity\":\"2025-04-21T00:56:06.000Z\",\"session_end\":\"2025-04-21T00:56:06.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:06 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:10 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:10"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:15 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:15"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:30 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:30"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:35"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:40 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:40"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:45 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:45"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:48 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:50 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:53 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:56:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:56:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:56:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:57:00 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:57:00"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:57:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:57:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:57:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:57:05"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:57:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:57:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:57:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:57:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:57:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:57:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:58:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:58:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:58:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:58:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:59:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 00:59:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:00:59:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 00:59:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:00:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:00:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:00:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:00:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:01:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:01:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:01:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:01:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:01:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:01:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:00 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:00"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:09 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:09"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:14 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:19 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:34"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:44 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:48 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:49 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:49"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:53 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:54 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:54"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:02:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:02:59 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:02:59"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:03:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:03:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:03:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:09 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:03:09"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:03:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:14 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:03:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:03:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:19 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:03:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:03:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:03:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:03:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:03:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:03:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:04:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:04:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:04:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:04:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:05:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:05:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:05:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:05:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:06:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:06:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:06:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:06:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:07:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:07:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:07:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:07:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:07:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:08:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:08:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:08:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:08:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:08:29"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:68:21)","timestamp":"2025-04-21 01:08:29"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:79:21)","timestamp":"2025-04-21 01:08:29"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:90:21)","timestamp":"2025-04-21 01:08:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.voucher_logs' doesn't exist","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'd65bd6c1-6565-41e1-a160-cab71d0dc8be'\n        ORDER BY vl.created_at DESC","sqlMessage":"Table 'vms_production.voucher_logs' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.voucher_logs' doesn't exist\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:101:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:08:29"}
{"level":"info","message":"Cleaned up 1 stale sessions due to inactivity","service":"vms-server","timestamp":"2025-04-21 01:09:03"}
{"level":"info","message":"Expired session: 71cf1e4c-dec4-41ab-9c48-b288dec1c5f7 for user 21d61814-cd59-4777-a0a0-0fdd9b085c01, inactive for 60 minutes","service":"vms-server","timestamp":"2025-04-21 01:09:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:09:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:09:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:09:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 355 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:09:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:09:34"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:09:52"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:97:21)","timestamp":"2025-04-21 01:09:52"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:108:21)","timestamp":"2025-04-21 01:09:52"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:125:21)","timestamp":"2025-04-21 01:09:52"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.voucher_logs' doesn't exist","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'b666818c-c26f-42d5-9c6c-febfb07028c4'\n        ORDER BY vl.created_at DESC","sqlMessage":"Table 'vms_production.voucher_logs' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.voucher_logs' doesn't exist\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:136:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:09:52"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:10:17"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 01:10:17"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 01:10:17"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 01:10:17"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:10:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 535 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:10:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:10:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 535 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:10:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:10:32"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:97:21)","timestamp":"2025-04-21 01:10:32"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:108:21)","timestamp":"2025-04-21 01:10:32"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:125:21)","timestamp":"2025-04-21 01:10:32"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.voucher_logs' doesn't exist","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'b84e5d49-da21-4c31-93e4-a4d6b057b90a'\n        ORDER BY vl.created_at DESC","sqlMessage":"Table 'vms_production.voucher_logs' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.voucher_logs' doesn't exist\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:136:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:10:32"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-04-21 01:10:54"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:10:54"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-04-21 01:10:54"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-04-21 01:10:54"}
{"level":"info","message":"Cleaned up 1 stale sessions due to inactivity","service":"vms-server","timestamp":"2025-04-21 01:11:03"}
{"level":"info","message":"Expired session: 468bb4f3-2164-4911-a716-da1e78ce807d for user e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc, inactive for 60 minutes","service":"vms-server","timestamp":"2025-04-21 01:11:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:11:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:11:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:11:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:11:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:12:16"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:97:21)","timestamp":"2025-04-21 01:12:16"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:108:21)","timestamp":"2025-04-21 01:12:16"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:125:21)","timestamp":"2025-04-21 01:12:16"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'u.username' in 'field list'","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'efd9cbfd-9990-4739-bd75-537a88862837'\n        ORDER BY vl.created_at DESC","sqlMessage":"Unknown column 'u.username' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'u.username' in 'field list'\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:136:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:12:16"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:12:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:12:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:12:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:12:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:13:12"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:95:21)","timestamp":"2025-04-21 01:13:12"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:106:21)","timestamp":"2025-04-21 01:13:12"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:121:21)","timestamp":"2025-04-21 01:13:12"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'u.username' in 'field list'","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'af1a9a4c-6ed0-49d4-a0fe-2f6dff0ccd95'\n        ORDER BY vl.created_at DESC","sqlMessage":"Unknown column 'u.username' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'u.username' in 'field list'\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:132:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:13:12"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:13:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:13:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:13:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:13:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-04-21 01:14:08"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:96:21)","timestamp":"2025-04-21 01:14:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:14:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:14:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:14:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:14:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:15:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:15:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:15:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:15:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:16:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:16:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:16:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:16:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:17:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:17:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:17:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:17:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:18:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:18:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:18:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:18:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:19:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:19:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:19:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:19:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:20:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:20:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:20:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:20:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:21:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:21:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:21:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:21:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:22:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:22:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:22:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:22:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:23:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:23:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:23:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:23:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:24:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:24:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:24:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:24:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:24:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:24:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:24:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:24:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:24:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:24:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:24:59 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:24:59"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:04"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:09 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:09"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: e2aeead0-80de-4304-af96-df70d551b475, IP: **********","service":"vms-server","timestamp":"2025-04-21 01:25:09"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:09 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:09"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:09 +0000] \"GET /api/users?_t=1745198709791 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:09"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:19 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:19 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: e2aeead0-80de-4304-af96-df70d551b475","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: e2aeead0-80de-4304-af96-df70d551b475","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:19 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: e2aeead0-80de-4304-af96-df70d551b475","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"Found inactive session: {\"id\":\"e2aeead0-80de-4304-af96-df70d551b475\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T01:25:09.000Z\",\"last_activity\":\"2025-04-21T01:25:19.000Z\",\"session_end\":\"2025-04-21T01:25:19.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:19 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:26 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:26"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:31 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:31"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:36 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:36"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:41 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:48 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:53 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:25:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:25:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:25:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:01 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:01"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:11 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:16 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:16"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:21 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:21"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:31 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:31"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:35"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:41 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:41"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:46 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:46"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:48 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:53 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:53"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:26:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:26:58 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:26:58"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:01 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:01"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:03 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:08 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:11 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:11"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:13 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:13"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:16 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:16"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:18 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:21 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:21"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:23 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:23"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:25"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:28 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:28"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:30 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:30"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:33 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:33"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:35"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:38"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:40 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:40"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:43 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:43"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:45 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:45"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: 5f591f90-c21e-47d8-b38f-cde5b17e5c19, IP: **********","service":"vms-server","timestamp":"2025-04-21 01:27:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:47"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:50 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:50"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:27:52.391Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:52 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198872370 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:52"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:27:55"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:27:57.391Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:27:57 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198877372 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:27:57"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:00 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:00"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:28:02.402Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:02 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198882378 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:28:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:05 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:05"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:28:07.414Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:07 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198887383 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:28:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:10 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:10"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:28:12.391Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:12 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198892372 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:28:12"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:15 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:15"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:28:17.397Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:17 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198897373 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:28:17"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:20 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:20"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:28:22.396Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:22 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198902375 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:28:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:25 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:25"}
{"level":"info","message":"GET /vouchers request at 2025-04-21T01:28:27.399Z by user SAMUEL ASIEDU (AUDIT), query department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"info","message":"Found 37 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00002 -> APR00012","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00012 -> APR00022","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR00032 -> APR2025688","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025688 -> APR2025921","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"warn","message":"Gap detected in FINANCE voucher sequence: APR2025921 -> APR2025941","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"info","message":"Sample vouchers: APR00001 (bb6ea1ff-d780-4970-a079-e4f6e7be2971): PENDING SUBMISSION, APR00002 (4274f845-ee82-4852-903e-034765c0aa9f): VOUCHER CERTIFIED, APR00012 (799c22d3-4036-431b-a1e3-35d5ab5b5732): VOUCHER CERTIFIED","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:27 +0000] \"GET /api/vouchers?department=FINANCE&timestamp=1745198907378 HTTP/1.1\" 200 39983 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:28:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:30 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:30"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:35 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:35"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: ed6e0a74-ad7e-40df-aedb-fcd9eb812322, IP: **********","service":"vms-server","timestamp":"2025-04-21 01:28:36"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:36 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:36"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:36 +0000] \"GET /api/users?_t=1745198916154 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:36"}
{"level":"info","message":"Generated voucher ID: APR2025962 for department: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Created voucher with ID: d5e0f664-af19-4687-af9e-4b2bee737bea, Voucher ID: APR2025962","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcasting voucher update: created for voucher APR2025962 in department FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcasting to 0 connected clients","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Found 0 clients in department FINANCE or with admin access","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Emitting to department room: FINANCE","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcasting created for voucher: d5e0f664-af19-4687-af9e-4b2bee737bea","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcasting to 0 connected clients","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcast complete for voucher d5e0f664-af19-4687-af9e-4b2bee737bea","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcast complete for voucher APR2025962","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcasting created for voucher: d5e0f664-af19-4687-af9e-4b2bee737bea","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcasting to 0 connected clients","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"Broadcast complete for voucher d5e0f664-af19-4687-af9e-4b2bee737bea","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:48 +0000] \"POST /api/vouchers HTTP/1.1\" 201 953 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:48"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198936325', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-28-56', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:28:56"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198936325', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-28-56', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:28:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:28:56 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:28:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:29:06 +0000] \"GET /api/users?_t=1745198946158 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:29:06"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198948661', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-29-08', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:08"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198948661', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-29-08', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:29:08 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:29:08"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198959171', dispatch_to_audit_by = 'Test User', dispatched_by = 'Test User', dispatch_time = '2025-04-21_01-29-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198959171', dispatch_to_audit_by = 'Test User', dispatched_by = 'Test User', dispatch_time = '2025-04-21_01-29-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:29:19 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:29:19"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged out successfully. Session ID: 5f591f90-c21e-47d8-b38f-cde5b17e5c19","service":"vms-server","timestamp":"2025-04-21 01:29:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:29:27 +0000] \"POST /api/auth/logout HTTP/1.1\" 200 37 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:29:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:29:29 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:29:29"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: ed6e0a74-ad7e-40df-aedb-fcd9eb812322","service":"vms-server","timestamp":"2025-04-21 01:29:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:22 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:22"}
{"level":"warn","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) attempted to access with an inactive session: 5f591f90-c21e-47d8-b38f-cde5b17e5c19","service":"vms-server","timestamp":"2025-04-21 01:57:22"}
{"level":"info","message":"Found inactive session: {\"id\":\"5f591f90-c21e-47d8-b38f-cde5b17e5c19\",\"user_id\":\"21d61814-cd59-4777-a0a0-0fdd9b085c01\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T01:27:47.000Z\",\"last_activity\":\"2025-04-21T01:29:27.000Z\",\"session_end\":\"2025-04-21T01:29:27.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 01:57:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:22 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:22"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:24 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:24 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:24 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:24"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:29 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:29"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:34 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:34"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:38 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:38"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: ed6e0a74-ad7e-40df-aedb-fcd9eb812322","service":"vms-server","timestamp":"2025-04-21 01:57:39"}
{"level":"info","message":"Found inactive session: {\"id\":\"ed6e0a74-ad7e-40df-aedb-fcd9eb812322\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T01:28:36.000Z\",\"last_activity\":\"2025-04-21T01:29:29.000Z\",\"session_end\":\"2025-04-21T01:29:29.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 01:57:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:39 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:39 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:39 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:39 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:39"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:40 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:40"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:44 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:44"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:45 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:45"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:49 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:49"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: d8db2264-991b-45f4-a66a-f465cce4ec3b, IP: **********","service":"vms-server","timestamp":"2025-04-21 01:57:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:50 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:50 +0000] \"GET /api/users?_t=1745200670043 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:57:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:54 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:54"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:57:59 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:57:59"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:04 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:58:04"}
{"level":"info","message":"User SAMUEL ASIEDU (21d61814-cd59-4777-a0a0-0fdd9b085c01) logged in successfully. Session ID: c06ae452-4334-4228-bacc-c3f68d2063e0, IP: **********","service":"vms-server","timestamp":"2025-04-21 01:58:08"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:08 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 01:58:08"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200698922', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-58-18', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:58:18"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200698922', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-58-18', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:58:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:18 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:18"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:20 +0000] \"GET /api/users?_t=1745200700049 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:20"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:50 +0000] \"GET /api/users?_t=1745200730708 HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:50 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:50 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:50 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:50 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:50 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:58:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:58:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:59:00 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:59:00"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 01:59:02"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: d281af17-3dcd-4b5f-9ddb-78a3dfbe46ef, IP: **********","service":"vms-server","timestamp":"2025-04-21 01:59:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:59:02 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:59:02"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:59:02 +0000] \"GET /api/users?_t=1745200742912 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:59:02"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200759586', dispatch_to_audit_by = 'SAMMY', dispatched_by = 'SAMMY', dispatch_time = '2025-04-21_01-59-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200759586', dispatch_to_audit_by = 'SAMMY', dispatched_by = 'SAMMY', dispatch_time = '2025-04-21_01-59-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:19"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:59:19 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:59:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200767262', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-59-27', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:27"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200767262', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-59-27', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:59:27 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:59:27"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:01:59:32 +0000] \"GET /api/users?_t=1745200772926 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 01:59:32"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:03 +0000] \"GET /api/users?_t=1745200803712 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:03"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:33 +0000] \"GET /api/users?_t=1745200833717 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:33"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200851358', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-00-51', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:00:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200851358', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-00-51', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:00:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:51 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:54 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:54"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: d281af17-3dcd-4b5f-9ddb-78a3dfbe46ef","service":"vms-server","timestamp":"2025-04-21 02:00:54"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: d281af17-3dcd-4b5f-9ddb-78a3dfbe46ef","service":"vms-server","timestamp":"2025-04-21 02:00:54"}
{"level":"info","message":"Found inactive session: {\"id\":\"d281af17-3dcd-4b5f-9ddb-78a3dfbe46ef\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T01:59:02.000Z\",\"last_activity\":\"2025-04-21T02:00:54.000Z\",\"session_end\":\"2025-04-21T02:00:54.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 02:00:54"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:54 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:54"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:55 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:55 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:55 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:55"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:00:56 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:00:56"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:01:01 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:01:01"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:01:06 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:01:06"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) has 1 active sessions but will be allowed to login again","service":"vms-server","timestamp":"2025-04-21 02:01:06"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged in successfully. Session ID: 1e3cc2fb-3f0e-4bce-84ff-d9220afad140, IP: **********","service":"vms-server","timestamp":"2025-04-21 02:01:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:01:06 +0000] \"POST /api/auth/login HTTP/1.1\" 200 545 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:01:06"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:01:06 +0000] \"GET /api/users?_t=1745200866985 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:01:06"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200874768', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-01-14', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:01:14"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200874768', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-01-14', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:01:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:01:14 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:01:14"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:01:37 +0000] \"GET /api/users?_t=1745200897713 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:01:37"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:02:07 +0000] \"GET /api/users?_t=1745200927718 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:02:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:02:37 +0000] \"GET /api/users?_t=1745200957710 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:02:37"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:07 +0000] \"GET /api/users?_t=1745200987708 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:07"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:37 +0000] \"GET /api/users?_t=1745201016997 HTTP/1.1\" 200 1652 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:37"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745201025315', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-03-45', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:03:45"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745201025315', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-03-45', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:03:45"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:45 +0000] \"PUT /api/vouchers/d5e0f664-af19-4687-af9e-4b2bee737bea HTTP/1.1\" 500 36 \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:45"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:48 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:48 +0000] \"POST /api/auth/logout HTTP/1.1\" - - \"http://localhost/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 1e3cc2fb-3f0e-4bce-84ff-d9220afad140","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) logged out successfully. Session ID: 1e3cc2fb-3f0e-4bce-84ff-d9220afad140","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:48 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"warn","message":"User FELIX AYISI (81bdc1b0-beb9-46bd-a4ef-42c227bf3b69) attempted to access with an inactive session: 1e3cc2fb-3f0e-4bce-84ff-d9220afad140","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"Found inactive session: {\"id\":\"1e3cc2fb-3f0e-4bce-84ff-d9220afad140\",\"user_id\":\"81bdc1b0-beb9-46bd-a4ef-42c227bf3b69\",\"user_name\":null,\"department\":null,\"socket_id\":null,\"session_start\":\"2025-04-21T02:01:06.000Z\",\"last_activity\":\"2025-04-21T02:03:48.000Z\",\"session_end\":\"2025-04-21T02:03:48.000Z\",\"client_ip\":\"**********\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"is_active\":0}","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:48 +0000] \"GET /api/auth/me HTTP/1.1\" 401 86 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:48"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:49 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:49"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:50 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 02:03:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:50 +0000] \"POST /api/auth/logout HTTP/1.1\" 401 35 \"http://localhost/audit-dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 02:03:50"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:51 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/?logout=true\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","service":"vms-server","timestamp":"2025-04-21 02:03:51"}
{"level":"info","message":"::ffff:********** - - [21/Apr/2025:02:03:54 +0000] \"GET /api/auth/users-by-department HTTP/1.1\" 200 715 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"vms-server","timestamp":"2025-04-21 02:03:54"}
