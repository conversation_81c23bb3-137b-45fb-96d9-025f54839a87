{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:42"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:43"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:44"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:45"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:47"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:48"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:50"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:51"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:53"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:54"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:56"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:57"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:36:59"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:00"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:02"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:03"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:05"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:07"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:08"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:09"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:11"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:12"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:14"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:15"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:16"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:18"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:19"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:20"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:21"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:23"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:24"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:26"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:27"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:28"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:29"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:31"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:33"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:34"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:35"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:37"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:38"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:39"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:40"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:42"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:43"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:44"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:45"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:46"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:48"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:49"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:50"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:51"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:53"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:54"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:55"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:56"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:58"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:37:59"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:00"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:01"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:03"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:04"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:05"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:06"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:08"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:09"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:10"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:12"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:13"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:14"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:15"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:17"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:18"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:19"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:20"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:21"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:22"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:24"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:25"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:26"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:27"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:28"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:30"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:31"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:32"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:33"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:34"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:35"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:36"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:38"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:39"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:40"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:41"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:43"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:44"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:45"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:46"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:48"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:49"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:50"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:51"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:53"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:54"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:55"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:56"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:58"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:38:59"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:00"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:01"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:02"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:04"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:05"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:06"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:07"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:09"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:10"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:11"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:13"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:14"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:16"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:17"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:19"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:20"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:21"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:23"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:24"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:25"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:26"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:28"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:30"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:31"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:33"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:34"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:36"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:37"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:38"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:40"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:41"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:43"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:44"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:46"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:48"}
{"address":null,"code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: bind EADDRINUSE null:8080","port":8080,"service":"vms-server","stack":"Error: bind EADDRINUSE null:8080\n    at listenOnPrimaryHandle (node:net:2018:18)\n    at shared (node:internal/cluster/child:157:3)\n    at Worker.<anonymous> (node:internal/cluster/child:110:7)\n    at process.onInternalMessage (node:internal/cluster/utils:49:5)\n    at process.emit (node:events:530:35)\n    at emit (node:internal/child_process:949:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)","syscall":"bind","timestamp":"2025-04-17 20:39:50"}
{"level":"error","message":"Error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"vms-server","timestamp":"2025-04-17 20:44:10"}
{"level":"error","message":"Error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"vms-server","timestamp":"2025-04-17 20:44:28"}
{"level":"error","message":"Error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"vms-server","timestamp":"2025-04-17 23:36:52"}
{"level":"error","message":"Error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"vms-server","timestamp":"2025-04-17 23:37:02"}
{"level":"error","message":"Create voucher error: getTransaction is not defined","service":"vms-server","stack":"ReferenceError: getTransaction is not defined\n    at file:///C:/VMS%203.0/server/dist/routes/vouchers.js:92:28\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/VMS%203.0/server/dist/middleware/auth.js:42:13\n    at C:\\VMS 3.0\\server\\node_modules\\jsonwebtoken\\verify.js:261:12","timestamp":"2025-04-18 01:48:36"}
{"level":"error","message":"Create voucher error: getTransaction is not defined","service":"vms-server","stack":"ReferenceError: getTransaction is not defined\n    at file:///C:/VMS%203.0/server/dist/routes/vouchers.js:92:28\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/VMS%203.0/server/dist/middleware/auth.js:42:13\n    at C:\\VMS 3.0\\server\\node_modules\\jsonwebtoken\\verify.js:261:12","timestamp":"2025-04-18 02:09:32"}
{"level":"error","message":"Create voucher error: getTransaction is not defined","service":"vms-server","stack":"ReferenceError: getTransaction is not defined\n    at file:///C:/VMS%203.0/server/dist/routes/vouchers.js:92:28\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/VMS%203.0/server/dist/middleware/auth.js:42:13\n    at C:\\VMS 3.0\\server\\node_modules\\jsonwebtoken\\verify.js:261:12","timestamp":"2025-04-18 02:10:11"}
{"level":"error","message":"Create voucher error: getTransaction is not defined","service":"vms-server","stack":"ReferenceError: getTransaction is not defined\n    at file:///C:/VMS%203.0/server/dist/routes/vouchers.js:92:28\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\VMS 3.0\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/VMS%203.0/server/dist/middleware/auth.js:42:13\n    at C:\\VMS 3.0\\server\\node_modules\\jsonwebtoken\\verify.js:261:12","timestamp":"2025-04-18 02:11:18"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:13:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:14:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:15:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:16:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:17:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:18:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:19:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:20:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:21:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:22:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:23:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:24:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:25:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:26:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:27:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:28:59"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:30:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:31:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:32:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:33:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:34:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:35:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:36:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:37:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:38:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:39:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:40:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:41:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:42:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:43:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:44:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:45:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:46:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:47:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:48:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:49:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:50:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:51:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:52:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:53:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:54:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:55:00"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:55:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:56:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:57:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:58:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 09:59:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:00:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:01:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:02:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:03:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:04:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:05:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:06:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:07:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:09:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:10:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:11:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:12:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:13:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:14:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:15:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:16:17"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:16:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:17:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:18:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:19:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:20:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:21:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:22:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:23:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:24:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:25:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:31:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:32:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:33:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:34:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:35:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:36:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:37:33"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:37:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:38:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:39:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:40:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:41:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:42:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:43:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:44:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:45:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 10:46:48"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:28:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:29:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:30:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:31:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:32:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:33:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:34:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:35:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:36:34"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:37:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:38:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:39:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:40:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:41:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:42:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:43:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:44:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:45:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:46:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:47:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:48:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:49:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:50:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:51:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:52:28"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:52:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:53:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:54:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:55:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:56:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:57:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:58:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 12:59:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:00:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:01:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:02:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:03:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:04:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:05:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:06:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:07:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:08:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:09:57"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:10:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:11:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:12:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:13:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:14:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:15:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:16:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:17:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:18:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:19:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:20:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:21:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:22:11"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at cleanupStaleSessions (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:26:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:27:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:28:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:29:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:30:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:31:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:32:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:33:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:34:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:35:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:36:52"}
{"level":"error","message":"Error cleaning up stale sessions: Cannot read properties of undefined (reading 'length')","service":"vms-server","stack":"TypeError: Cannot read properties of undefined (reading 'length')\n    at Timeout.cleanupStaleSessions [as _onTimeout] (file:///C:/VMS%203.0/server/dist/tasks/session-cleanup.js:14:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-18 13:37:52"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.active_sessions' doesn't exist","service":"vms-server","sql":"SELECT *, TIMESTAMPDIFF(MINUTE, last_activity, NOW()) as inactive_minutes\n       FROM active_sessions\n       WHERE is_active = TRUE\n       AND TIMESTAMPDIFF(MINUTE, last_activity, NOW()) >= 15","sqlMessage":"Table 'vms_production.active_sessions' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.active_sessions' doesn't exist\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:239:38)\n    at cleanupStaleSessions (file:///app/dist/tasks/session-cleanup.js:13:37)\n    at scheduleSessionCleanup (file:///app/dist/tasks/session-cleanup.js:39:5)\n    at Server.<anonymous> (file:///app/dist/index.js:64:13)\n    at Object.onceWrapper (node:events:631:28)\n    at Server.emit (node:events:529:35)\n    at emitListeningNT (node:net:1851:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:81:21)","timestamp":"2025-04-18 17:06:32"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.active_sessions' doesn't exist","service":"vms-server","sql":"SELECT *, TIMESTAMPDIFF(MINUTE, last_activity, NOW()) as inactive_minutes\n       FROM active_sessions\n       WHERE is_active = TRUE\n       AND TIMESTAMPDIFF(MINUTE, last_activity, NOW()) >= 15","sqlMessage":"Table 'vms_production.active_sessions' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.active_sessions' doesn't exist\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:239:38)\n    at cleanupStaleSessions (file:///app/dist/tasks/session-cleanup.js:13:37)\n    at scheduleSessionCleanup (file:///app/dist/tasks/session-cleanup.js:39:5)\n    at Server.<anonymous> (file:///app/dist/index.js:64:13)\n    at Object.onceWrapper (node:events:631:28)\n    at Server.emit (node:events:529:35)\n    at emitListeningNT (node:net:1851:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:81:21)","timestamp":"2025-04-18 17:06:32"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000236462', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-16', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '1cb269d6-4283-4e52-8c5d-0c85e47faf60'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:16"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000236462', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-16', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '1cb269d6-4283-4e52-8c5d-0c85e47faf60'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:16"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000252140', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-32', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '1cb269d6-4283-4e52-8c5d-0c85e47faf60'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:32"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000252140', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-32', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '1cb269d6-4283-4e52-8c5d-0c85e47faf60'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:32"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000267302', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '7842a054-1116-4124-8761-06e8b552185c'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000267302', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '7842a054-1116-4124-8761-06e8b552185c'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000267302', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '1cb269d6-4283-4e52-8c5d-0c85e47faf60'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000267302', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '1cb269d6-4283-4e52-8c5d-0c85e47faf60'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000267302', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '8c878fa0-d29b-4c04-8fd3-a4a39d580e16'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000267302', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-17-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '8c878fa0-d29b-4c04-8fd3-a4a39d580e16'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:17:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000327973', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-18-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'c7390876-109c-4df6-a19e-96d46ed76812'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:18:48"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000327973', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-18-47', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'c7390876-109c-4df6-a19e-96d46ed76812'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:18:48"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000345009', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-19-05', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'c7390876-109c-4df6-a19e-96d46ed76812'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:19:05"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000345009', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-19-05', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'c7390876-109c-4df6-a19e-96d46ed76812'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:19:05"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000361629', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-19-21', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'c7390876-109c-4df6-a19e-96d46ed76812'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:19:21"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745000361629', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-19_06-19-21', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'c7390876-109c-4df6-a19e-96d46ed76812'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:249:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-18 18:19:21"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745189670427', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-20_22-54-30', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'bb6ea1ff-d780-4970-a079-e4f6e7be2971'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-20 22:54:30"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745189670427', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-20_22-54-30', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'bb6ea1ff-d780-4970-a079-e4f6e7be2971'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-20 22:54:30"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-GJgcXNIXdvKqv2UWAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'GJgcXNIXdvKqv2UWAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:07:59"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-tnGCOkYEcT5FwtKQAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'tnGCOkYEcT5FwtKQAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:08:58"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('81bdc1b0-beb9-46bd-a4ef-42c227bf3b69-7LwXInG7Qwr2bdNXAAAB', '81bdc1b0-beb9-46bd-a4ef-42c227bf3b69', 'FELIX AYISI', 'FINANCE', '7LwXInG7Qwr2bdNXAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:10:38"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-KK5OhPT7y8uz-KGpAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'KK5OhPT7y8uz-KGpAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:24:27"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-ux_cVi8q44ViBynAAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'ux_cVi8q44ViBynAAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:27:07"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-4BXWsxRtfuhIhbD-AAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', '4BXWsxRtfuhIhbD-AAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:31:35"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('81bdc1b0-beb9-46bd-a4ef-42c227bf3b69-ZJUpY4GlkDRrUEFhAAAB', '81bdc1b0-beb9-46bd-a4ef-42c227bf3b69', 'FELIX AYISI', 'FINANCE', 'ZJUpY4GlkDRrUEFhAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:32:08"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-U3yxsf7hdtvmtP4uAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'U3yxsf7hdtvmtP4uAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:35:29"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-fv7dJUlK1If0oKNBAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'fv7dJUlK1If0oKNBAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:41:56"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('81bdc1b0-beb9-46bd-a4ef-42c227bf3b69-xbyFrV5IBuTInc0CAAAB', '81bdc1b0-beb9-46bd-a4ef-42c227bf3b69', 'FELIX AYISI', 'FINANCE', 'xbyFrV5IBuTInc0CAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:45:19"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('81bdc1b0-beb9-46bd-a4ef-42c227bf3b69-DFIHQ3-sZySljoh-AAAB', '81bdc1b0-beb9-46bd-a4ef-42c227bf3b69', 'FELIX AYISI', 'FINANCE', 'DFIHQ3-sZySljoh-AAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:45:40"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc-8yu038yJs5dAlitoAAAD', 'e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc', 'WILLIAM AKUAMOAH', 'AUDIT', '8yu038yJs5dAlitoAAAD', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:49:49"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-JNsluT-5ONoqVglIAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'JNsluT-5ONoqVglIAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:50:12"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-uQ-QYSX_J1u_sXXjAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'uQ-QYSX_J1u_sXXjAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:56:43"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc-R4xOS9tEBJ1xJi1xAAAB', 'e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc', 'WILLIAM AKUAMOAH', 'AUDIT', 'R4xOS9tEBJ1xJi1xAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-20 23:57:42"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-F88gCEvEZrQ23rerAAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'F88gCEvEZrQ23rerAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-21 00:02:58"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc-ij1hS4iYd4k0WtY_AAAB', 'e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc', 'WILLIAM AKUAMOAH', 'AUDIT', 'ij1hS4iYd4k0WtY_AAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-21 00:03:15"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('21d61814-cd59-4777-a0a0-0fdd9b085c01-AfuXyiPcQ38IrAN-AAAB', '21d61814-cd59-4777-a0a0-0fdd9b085c01', 'SAMUEL ASIEDU', 'AUDIT', 'AfuXyiPcQ38IrAN-AAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-21 00:07:35"}
{"code":"ER_DATA_TOO_LONG","errno":1406,"level":"error","message":"Data too long for column 'id' at row 1","service":"vms-server","sql":"INSERT INTO active_sessions (id, user_id, user_name, department, socket_id, session_start, last_activity, is_active)\n         VALUES ('e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc-VAsrRn6p1d1ukOJSAAAB', 'e4fdf0d3-bbfc-46f3-b312-4bf12dee18bc', 'WILLIAM AKUAMOAH', 'AUDIT', 'VAsrRn6p1d1ukOJSAAAB', NOW(), NOW(), TRUE)\n         ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = TRUE","sqlMessage":"Data too long for column 'id' at row 1","sqlState":"22001","stack":"Error: Data too long for column 'id' at row 1\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at Namespace.<anonymous> (file:///app/dist/socket/socketHandlers.js:90:13)\n    at Namespace.emit (node:events:517:28)\n    at Namespace.emitReserved (/app/node_modules/socket.io/dist/typed-events.js:56:22)\n    at Namespace._doConnect (/app/node_modules/socket.io/dist/namespace.js:276:14)\n    at /app/node_modules/socket.io/dist/namespace.js:238:22\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-04-21 00:07:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745196293897', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_00-44-53', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '37d4a655-1d18-41c3-a1e3-67391dd3f4b4'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 00:44:53"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745196293897', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_00-44-53', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = '37d4a655-1d18-41c3-a1e3-67391dd3f4b4'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 00:44:53"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:68:21)","timestamp":"2025-04-21 01:08:29"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:79:21)","timestamp":"2025-04-21 01:08:29"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:90:21)","timestamp":"2025-04-21 01:08:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.voucher_logs' doesn't exist","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'd65bd6c1-6565-41e1-a160-cab71d0dc8be'\n        ORDER BY vl.created_at DESC","sqlMessage":"Table 'vms_production.voucher_logs' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.voucher_logs' doesn't exist\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:101:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:08:29"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:97:21)","timestamp":"2025-04-21 01:09:52"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:108:21)","timestamp":"2025-04-21 01:09:52"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:125:21)","timestamp":"2025-04-21 01:09:52"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.voucher_logs' doesn't exist","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'b666818c-c26f-42d5-9c6c-febfb07028c4'\n        ORDER BY vl.created_at DESC","sqlMessage":"Table 'vms_production.voucher_logs' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.voucher_logs' doesn't exist\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:136:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:09:52"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:97:21)","timestamp":"2025-04-21 01:10:32"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:108:21)","timestamp":"2025-04-21 01:10:32"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:125:21)","timestamp":"2025-04-21 01:10:32"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'vms_production.voucher_logs' doesn't exist","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'b84e5d49-da21-4c31-93e4-a4d6b057b90a'\n        ORDER BY vl.created_at DESC","sqlMessage":"Table 'vms_production.voucher_logs' doesn't exist","sqlState":"42S02","stack":"Error: Table 'vms_production.voucher_logs' doesn't exist\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:136:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:10:32"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:97:21)","timestamp":"2025-04-21 01:12:16"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:108:21)","timestamp":"2025-04-21 01:12:16"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:125:21)","timestamp":"2025-04-21 01:12:16"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'u.username' in 'field list'","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'efd9cbfd-9990-4739-bd75-537a88862837'\n        ORDER BY vl.created_at DESC","sqlMessage":"Unknown column 'u.username' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'u.username' in 'field list'\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:136:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:12:16"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:95:21)","timestamp":"2025-04-21 01:13:12"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to AUDIT: PROCESSING. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:106:21)","timestamp":"2025-04-21 01:13:12"}
{"level":"error","message":"Error updating voucher status: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT","service":"vms-server","stack":"Error: Cannot transition from PENDING SUBMISSION to VOUCHER CERTIFIED. Allowed transitions: PENDING RECEIPT\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:121:21)","timestamp":"2025-04-21 01:13:12"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'u.username' in 'field list'","service":"vms-server","sql":"SELECT \n          vl.*,\n          u.username as user_name,\n          u.department\n        FROM voucher_logs vl\n        LEFT JOIN users u ON vl.user_id = u.id\n        WHERE vl.voucher_id = 'af1a9a4c-6ed0-49d4-a0fe-2f6dff0ccd95'\n        ORDER BY vl.created_at DESC","sqlMessage":"Unknown column 'u.username' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'u.username' in 'field list'\n    at PromisePool.query (C:\\VMS 4.0\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at VoucherService.getVoucherHistory (file:///C:/VMS%204.0/server/dist/services/voucherService.js:145:39)\n    at testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:132:42)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-21 01:13:12"}
{"level":"error","message":"Error updating voucher status: Required flag \"sentToAudit\" not set for this transition","service":"vms-server","stack":"Error: Required flag \"sentToAudit\" not set for this transition\n    at VoucherService.updateVoucherStatus (file:///C:/VMS%204.0/server/dist/services/voucherService.js:20:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async testVoucherService (file:///C:/VMS%204.0/server/setup/test-voucher-service.js:96:21)","timestamp":"2025-04-21 01:14:08"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198936325', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-28-56', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:28:56"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198936325', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-28-56', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:28:56"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198948661', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-29-08', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:08"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198948661', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-29-08', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:08"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198959171', dispatch_to_audit_by = 'Test User', dispatched_by = 'Test User', dispatch_time = '2025-04-21_01-29-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745198959171', dispatch_to_audit_by = 'Test User', dispatched_by = 'Test User', dispatch_time = '2025-04-21_01-29-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:29:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200698922', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-58-18', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:58:18"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200698922', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-58-18', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:58:18"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200759586', dispatch_to_audit_by = 'SAMMY', dispatched_by = 'SAMMY', dispatch_time = '2025-04-21_01-59-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200759586', dispatch_to_audit_by = 'SAMMY', dispatched_by = 'SAMMY', dispatch_time = '2025-04-21_01-59-19', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:19"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200767262', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-59-27', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:27"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200767262', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_01-59-27', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 01:59:27"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200851358', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-00-51', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:00:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200851358', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-00-51', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:00:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200874768', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-01-14', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:01:14"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745200874768', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-01-14', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:01:14"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745201025315', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-03-45', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:03:45"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'certified' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET status = 'PENDING RECEIPT', sent_to_audit = true, batch_id = 'batch1745201025315', dispatch_to_audit_by = 'FELIX AYISI', dispatched_by = 'FELIX AYISI', dispatch_time = '2025-04-21_02-03-45', dispatched = false, certified = false, rejected = false, is_returned = false, pending_return = false WHERE id = 'd5e0f664-af19-4687-af9e-4b2bee737bea'","sqlMessage":"Unknown column 'certified' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'certified' in 'field list'\n    at PromisePool.query (/app/node_modules/mysql2/lib/promise/pool.js:36:22)\n    at query (file:///app/dist/database/db.js:258:38)\n    at file:///app/dist/routes/vouchers.js:250:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-04-21 02:03:45"}
