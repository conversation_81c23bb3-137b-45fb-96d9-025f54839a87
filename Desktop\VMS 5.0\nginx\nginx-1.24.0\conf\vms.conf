# VMS Configuration

server {
        listen 80;
        server_name localhost ************** ************;

        # Serve frontend static files
        location / {
            root html/vms;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        # Proxy API requests to backend
        location /api/ {
            # Use the server's IP address instead of localhost
            proxy_pass http://**************:8080/api/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            # Add CORS headers
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        }

        # Proxy WebSocket requests to backend
        location /socket.io/ {
            # Use the server's IP address instead of localhost
            proxy_pass http://**************:8081/socket.io/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            # Add CORS headers
            add_header 'Access-Control-Allow-Origin' '*';
        }

        # Error pages
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root html;
        }
    }
