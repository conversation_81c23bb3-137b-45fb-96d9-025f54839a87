<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Local Storage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Clear Local Storage</h1>
    <p>This utility will clear the local storage for the Voucher Management System, which will reset the application state and force it to reload user data from the source files.</p>
    
    <button id="clearBtn">Clear Local Storage</button>
    <button id="viewBtn">View Current Storage</button>
    
    <div class="result" id="result"></div>
    
    <script>
        document.getElementById('clearBtn').addEventListener('click', function() {
            const vmsKey = 'voucher-management-system';
            const hadStorage = localStorage.getItem(vmsKey) !== null;
            
            localStorage.removeItem(vmsKey);
            
            const resultDiv = document.getElementById('result');
            if (hadStorage) {
                resultDiv.innerHTML = '<p>✅ Local storage cleared successfully!</p>' +
                    '<p>Please restart the application or refresh the main page to load the updated user data.</p>';
            } else {
                resultDiv.innerHTML = '<p>⚠️ No local storage found for the Voucher Management System.</p>';
            }
        });
        
        document.getElementById('viewBtn').addEventListener('click', function() {
            const vmsKey = 'voucher-management-system';
            const storage = localStorage.getItem(vmsKey);
            
            const resultDiv = document.getElementById('result');
            if (storage) {
                try {
                    const data = JSON.parse(storage);
                    resultDiv.innerHTML = '<p>Current storage content:</p>' +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } catch (e) {
                    resultDiv.innerHTML = '<p>Error parsing storage data: ' + e.message + '</p>';
                }
            } else {
                resultDiv.innerHTML = '<p>No local storage found for the Voucher Management System.</p>';
            }
        });
    </script>
</body>
</html>
