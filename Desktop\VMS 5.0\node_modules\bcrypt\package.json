{"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "5.1.1", "author": "<PERSON> (https://github.com/ncb000gt)", "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm ci --build-from-source && jest", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"@mapbox/node-pre-gyp": "^1.0.11", "node-addon-api": "^5.0.0"}, "devDependencies": {"jest": "^29.6.2"}, "contributors": ["<PERSON> <<EMAIL>> (https://github.com/Shadowfiend)", "<PERSON> <<EMAIL>> (https://github.com/thegoleffect)", "<PERSON> <<EMAIL>> (https://github.com/dtrejo)", "<PERSON> <<EMAIL>> (https://github.com/pixelglow)", "NewITFarmer.com <> (https://github.com/newitfarmer)", "<PERSON> <<EMAIL>> (https://github.com/alfredwesterveld)", "<PERSON>-Roy <<EMAIL>> (https://github.com/vincentcr)", "<PERSON> <<EMAIL>> (https://github.com/lloyd)", "<PERSON>htylman <<EMAIL>> (https://github.com/shtylman)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vadimg)", "<PERSON> <> (https://github.com/bnoordhuis)", "<PERSON> <<EMAIL>> (https://github.com/tootallnate)", "<PERSON> <<EMAIL>> (https://github.com/seanmonstar)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/weareu)", "Amito<PERSON> Swain <PERSON> <<EMAIL>> (https://github.com/Agathver)", "<PERSON> <<EMAIL>> (https://github.com/crutchcorn)", "<PERSON> <<EMAIL>> (https://github.com/NickNaso)"], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com", "remote_path": "kelektiv/node.bcrypt.js/releases/download/v{version}", "napi_versions": [3]}}